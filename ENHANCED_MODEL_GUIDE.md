# Enhanced SME Loan Default Prediction - Quick Start Guide

## 🎯 Objective
Improve SME loan default prediction accuracy from **60% to 85%+** using advanced machine learning techniques.

## 📁 Files Overview

### Main Notebook
- **`enhanced_model_building.ipynb`** - Complete enhanced ML pipeline in Jupyter notebook format

### Supporting Files
- **`enhanced_model_building.py`** - Python script version (alternative)
- **`run_enhanced_model.py`** - One-click setup and execution
- **`model_comparison.py`** - Compare original vs enhanced performance
- **`requirements_enhanced.txt`** - Required packages

## 🚀 Quick Start

### Option 1: Jupyter Notebook (Recommended)
1. **Open the notebook:**
   ```bash
   jupyter notebook enhanced_model_building.ipynb
   ```

2. **Run cells sequentially:**
   - **Cell 1-2**: Setup and imports
   - **Cell 3-7**: Data loading and exploration
   - **Cell 8-16**: Advanced feature engineering
   - **Cell 17-23**: Model setup and training
   - **Cell 24-34**: Evaluation and results

3. **Install packages if needed:**
   - Uncomment and run the installation cell (Cell 3)
   - Or manually: `pip install xgboost lightgbm optuna plotly`

### Option 2: Python Script
```bash
python run_enhanced_model.py
```

## 📊 Notebook Structure

### Section 1: Setup and Imports (Cells 1-2)
- Package installation helper
- Import all required libraries
- Check for XGBoost/LightGBM availability

### Section 2: Data Loading (Cells 3-7)
- Load and validate data files
- Explore target distribution
- Merge datasets for modeling

### Section 3: Feature Engineering (Cells 8-16)
- **Basic ratios**: Profitability, liquidity, leverage
- **Advanced metrics**: Altman Z-Score, DuPont analysis
- **Risk indicators**: Volatility measures, business model indicators
- **Alternative data**: Digital trust scores, customer satisfaction
- **Industry benchmarking**: Relative performance metrics
- **Data cleaning**: Outlier handling, missing value imputation

### Section 4: Model Building (Cells 17-23)
- **Data splitting**: Stratified train/test split
- **Feature selection**: SelectKBest with statistical tests
- **Class balancing**: BorderlineSMOTE for imbalanced data
- **Model setup**: 6+ advanced models including XGBoost, LightGBM
- **Ensemble methods**: Voting and Stacking classifiers

### Section 5: Evaluation (Cells 24-34)
- **Performance metrics**: Accuracy, ROC-AUC, cross-validation
- **Visualizations**: Performance comparison, ROC curves, confusion matrix
- **Feature importance**: Analysis for tree-based models
- **Model saving**: Artifacts saved to `enhanced_models/` directory
- **Final summary**: Comprehensive results and improvements

## 🎯 Expected Results

### Performance Improvements
| Metric | Original | Enhanced (Expected) | Improvement |
|--------|----------|-------------------|-------------|
| **Accuracy** | 60% | **80-85%** | +20-25% |
| **ROC-AUC** | 0.686 | **0.85-0.90** | +24-31% |
| **Features** | 17 | **40+** | +135% |

### Key Enhancements
✅ **Advanced Models**: XGBoost, LightGBM, Gradient Boosting  
✅ **Ensemble Methods**: Voting + Stacking classifiers  
✅ **Enhanced Features**: 40+ engineered features  
✅ **Better Sampling**: BorderlineSMOTE for class imbalance  
✅ **Feature Selection**: Statistical feature selection  
✅ **Cross-Validation**: Stratified K-fold validation  

## 📁 Output Files

After running the notebook, check the `enhanced_models/` directory:
- `best_model.joblib` - Trained best model
- `scaler.joblib` - Feature scaler
- `feature_selector.joblib` - Feature selector
- `selected_features.csv` - Selected feature names
- `model_performance.csv` - All model results
- `model_metadata.csv` - Experiment metadata

## 🔧 Troubleshooting

### Common Issues

1. **Missing packages:**
   ```bash
   pip install xgboost lightgbm optuna plotly
   ```

2. **Data files not found:**
   - Ensure you've run `data_synthesis.ipynb` first
   - Check that `data/` directory contains all CSV files

3. **Memory issues:**
   - Reduce `k_best` parameter in feature selection
   - Use fewer models or smaller datasets

4. **XGBoost/LightGBM not available:**
   - The notebook will skip these models automatically
   - Install manually: `pip install xgboost lightgbm`

### Performance Tips

1. **For faster execution:**
   - Reduce cross-validation folds (cv=3 instead of 5)
   - Use fewer estimators in ensemble models
   - Select fewer features (k=30 instead of 50)

2. **For better accuracy:**
   - Increase model complexity (more estimators)
   - Add more feature engineering
   - Try different sampling techniques

## 📈 Next Steps

1. **Run the enhanced model** and check results
2. **Compare performance** using `model_comparison.py`
3. **Fine-tune hyperparameters** if needed
4. **Deploy the best model** for production use

## 💡 Additional Improvements

If you want to push accuracy even higher:
- **Neural networks**: Add deep learning models
- **Time-series features**: If temporal data available
- **External data**: Economic indicators, industry trends
- **Advanced ensembling**: Bayesian model averaging
- **Hyperparameter optimization**: Use Optuna for automated tuning

---

**Ready to achieve 85%+ accuracy? Start with the Jupyter notebook!** 🚀
