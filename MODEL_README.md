# SME Loan Default Prediction Model Documentation

## Overview
This document provides detailed documentation for the SME Loan Default Prediction model, including the methodology, feature engineering process, and model evaluation metrics.

## Table of Contents
1. [Feature Engineering](#feature-engineering)
2. [Model Architecture](#model-architecture)
3. [Performance Metrics](#performance-metrics)
4. [Implementation Details](#implementation-details)

## Feature Engineering

### Financial Ratios
The model uses the following financial ratios as features:

1. **Profitability Ratios**
   - Profit Margin (Net Income / Revenue)
   - Return on Assets (Net Income / Total Assets)
   - Operating Margin ((Revenue - Operating Expense) / Revenue)

2. **Leverage Ratios**
   - Debt to Equity (Total Liabilities / Equity)
   - Liability to Asset (Total Liabilities / Total Assets)

3. **Liquidity Ratios**
   - Current Ratio ((Cash + Accounts Receivable) / Accounts Payable)
   - Cash Ratio (Cash / Accounts Payable)
   - Working Capital Ratio ((Cash + AR - AP) / Total Assets)

4. **Efficiency Ratios**
   - Asset Turnover (Revenue / Total Assets)
   - Inventory Turnover (COGS / Inventory)
   - Receivables Turnover (Revenue / Accounts Receivable)

### Alternative Data Features
1. Online Presence Score (0-10)
2. Customer Review Score (0-5)
3. Monthly Customer Complaints
4. Payment Gateway Transaction Volatility

### Business Characteristics
1. Years in Business
2. Geographic Location Risk Tier
3. Industry Sector (one-hot encoded)
4. Geographic Location (one-hot encoded)

## Model Architecture

### Data Preprocessing
1. **Feature Scaling**
   - StandardScaler applied to all numerical features
   - Categorical variables one-hot encoded

2. **Class Imbalance Handling**
   - SMOTE (Synthetic Minority Over-sampling Technique)
   - Balanced training set while maintaining test set distribution

### Model Pipeline
1. **Baseline Model**
   - Logistic Regression
   - Serves as a benchmark for more complex models

2. **Advanced Models**
   - Random Forest Classifier
   - Gradient Boosting Classifier

3. **Hyperparameter Tuning**
   - GridSearchCV for optimal parameters
   - 5-fold cross-validation

## Performance Metrics

The model's performance is evaluated using:

1. **Classification Metrics**
   - Accuracy
   - Precision
   - Recall
   - F1-Score
   - ROC-AUC Score

2. **Business Metrics**
   - Default Detection Rate
   - False Alarm Rate
   - Expected Cost Savings

## Implementation Details

### Model Training Process
1. Data splitting (80% train, 20% test)
2. Feature scaling
3. SMOTE application
4. Model training and validation
5. Performance evaluation

### Feature Importance
- Random Forest feature importance rankings
- Top features that drive default prediction

### Model Persistence
- Models saved using joblib
- Scaler and feature columns preserved for production use

## Usage Guidelines

### Required Dependencies
```python
scikit-learn>=0.24.0
pandas>=1.2.0
numpy>=1.19.0
imbalanced-learn>=0.8.0
```

### Prediction Pipeline
1. Load saved model and scaler
2. Preprocess new data using same feature engineering
3. Scale features using saved scaler
4. Make predictions using loaded model

### Monitoring and Maintenance
- Regular model retraining (quarterly recommended)
- Feature drift monitoring
- Performance metric tracking

## Performance Considerations
- Model optimized for balanced precision and recall
- False positives weighted against false negatives
- Regular validation against new data recommended

## Future Improvements
1. Additional alternative data sources integration
2. Deep learning models for complex patterns
3. Time-series features incorporation
4. API deployment for real-time predictions
