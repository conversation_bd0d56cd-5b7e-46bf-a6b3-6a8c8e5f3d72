"""
Model Comparison Script
Compare the original model with the enhanced model performance
"""

import pandas as pd
import numpy as np
import os
import joblib
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score

def load_original_model():
    """Load the original model if available"""
    try:
        if os.path.exists('saved_models/best_model.joblib'):
            model = joblib.load('saved_models/best_model.joblib')
            scaler = joblib.load('saved_models/scaler.joblib')
            features = pd.read_csv('saved_models/feature_columns.csv')['0'].tolist()
            return model, scaler, features
        else:
            return None, None, None
    except Exception as e:
        print(f"Error loading original model: {e}")
        return None, None, None

def load_enhanced_model():
    """Load the enhanced model if available"""
    try:
        if os.path.exists('enhanced_models/best_model.joblib'):
            model = joblib.load('enhanced_models/best_model.joblib')
            scaler = joblib.load('enhanced_models/scaler.joblib')
            selector = joblib.load('enhanced_models/feature_selector.joblib')
            features = pd.read_csv('enhanced_models/selected_features.csv')['0'].tolist()
            return model, scaler, selector, features
        else:
            return None, None, None, None
    except Exception as e:
        print(f"Error loading enhanced model: {e}")
        return None, None, None, None

def compare_models():
    """Compare original and enhanced models"""
    print("SME Loan Default Prediction - Model Comparison")
    print("=" * 60)
    
    # Check for performance files
    original_performance = None
    enhanced_performance = None
    
    # Load enhanced model performance if available
    if os.path.exists('enhanced_models/model_performance.csv'):
        enhanced_performance = pd.read_csv('enhanced_models/model_performance.csv')
        print("\nEnhanced Model Performance:")
        print(enhanced_performance.to_string(index=False))
        
        best_enhanced = enhanced_performance.loc[enhanced_performance['Accuracy'].idxmax()]
        print(f"\nBest Enhanced Model: {best_enhanced['Model']}")
        print(f"Enhanced Accuracy: {best_enhanced['Accuracy']:.4f}")
        print(f"Enhanced ROC-AUC: {best_enhanced['ROC_AUC']:.4f}")
    
    # Original model performance (from notebook output)
    original_accuracy = 0.60  # From the notebook results
    original_roc_auc = 0.686  # From the notebook results
    
    print(f"\nOriginal Model Performance:")
    print(f"Original Accuracy: {original_accuracy:.4f}")
    print(f"Original ROC-AUC: {original_roc_auc:.4f}")
    
    # Calculate improvements if enhanced model exists
    if enhanced_performance is not None:
        best_enhanced_acc = enhanced_performance['Accuracy'].max()
        best_enhanced_auc = enhanced_performance['ROC_AUC'].max()
        
        acc_improvement = ((best_enhanced_acc - original_accuracy) / original_accuracy) * 100
        auc_improvement = ((best_enhanced_auc - original_roc_auc) / original_roc_auc) * 100
        
        print(f"\n" + "=" * 60)
        print("IMPROVEMENT SUMMARY")
        print("=" * 60)
        print(f"Accuracy Improvement: {acc_improvement:+.1f}% ({original_accuracy:.3f} → {best_enhanced_acc:.3f})")
        print(f"ROC-AUC Improvement: {auc_improvement:+.1f}% ({original_roc_auc:.3f} → {best_enhanced_auc:.3f})")
        
        if best_enhanced_acc > 0.85:
            print("🎉 TARGET ACHIEVED: >85% accuracy reached!")
        else:
            print(f"📈 Progress: {best_enhanced_acc:.1%} accuracy (Target: 85%)")
    
    # Feature comparison
    print(f"\n" + "=" * 60)
    print("FEATURE COMPARISON")
    print("=" * 60)
    
    original_features = 17  # From the original model
    enhanced_features = 40  # From the enhanced model
    
    print(f"Original Features: {original_features}")
    print(f"Enhanced Features: {enhanced_features}+")
    print(f"Feature Increase: {enhanced_features - original_features} additional features")
    
    # Model comparison
    print(f"\n" + "=" * 60)
    print("MODEL ARCHITECTURE COMPARISON")
    print("=" * 60)
    
    print("Original Models:")
    print("  • Logistic Regression")
    print("  • Random Forest")
    
    print("\nEnhanced Models:")
    print("  • Logistic Regression (optimized)")
    print("  • Random Forest (optimized)")
    print("  • Extra Trees")
    print("  • Gradient Boosting")
    print("  • XGBoost (if available)")
    print("  • LightGBM (if available)")
    print("  • Voting Ensemble")
    print("  • Stacking Ensemble")
    
    # Technique comparison
    print(f"\n" + "=" * 60)
    print("TECHNIQUE IMPROVEMENTS")
    print("=" * 60)
    
    improvements = [
        ("Class Imbalance", "SMOTE", "BorderlineSMOTE"),
        ("Feature Selection", "None", "SelectKBest"),
        ("Cross-Validation", "Simple split", "Stratified K-fold"),
        ("Hyperparameters", "Basic grid", "Optimized grids"),
        ("Ensemble Methods", "None", "Voting + Stacking"),
        ("Feature Engineering", "Basic ratios", "40+ advanced features"),
        ("Outlier Handling", "None", "Percentile capping"),
        ("Industry Benchmarking", "None", "Relative metrics")
    ]
    
    for technique, original, enhanced in improvements:
        print(f"{technique:20} | {original:15} → {enhanced}")

def main():
    """Main execution function"""
    compare_models()
    
    print(f"\n" + "=" * 60)
    print("RECOMMENDATIONS")
    print("=" * 60)
    
    if not os.path.exists('enhanced_models/'):
        print("🚀 Run the enhanced model to see improvements:")
        print("   python run_enhanced_model.py")
    else:
        print("✅ Enhanced model results available!")
        print("📊 Check 'enhanced_models/model_performance.csv' for detailed metrics")
        print("🔍 Review console output from enhanced model run for insights")
    
    print("\n💡 Additional improvement suggestions:")
    print("   • Fine-tune hyperparameters further")
    print("   • Add more domain-specific features")
    print("   • Implement neural networks for complex patterns")
    print("   • Use time-series features if temporal data available")

if __name__ == "__main__":
    main()
