{"cells": [{"cell_type": "code", "execution_count": 2, "id": "2e3210d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 2500 entries, 0 to 2499\n", "Data columns (total 14 columns):\n", " #   Column               Non-Null Count  Dtype  \n", "---  ------               --------------  -----  \n", " 0   Financial_Record_ID  2500 non-null   int64  \n", " 1   SME_ID               2500 non-null   int64  \n", " 2   Year                 2500 non-null   int64  \n", " 3   Revenue              2500 non-null   float64\n", " 4   COGS                 2500 non-null   float64\n", " 5   Operating_Expense    2500 non-null   float64\n", " 6   Net_Income           2500 non-null   float64\n", " 7   Total_Assets         2500 non-null   float64\n", " 8   Cash_Holdings        2500 non-null   float64\n", " 9   Accounts_Receivable  2500 non-null   float64\n", " 10  Inventory            2500 non-null   float64\n", " 11  Total_Liabilities    2500 non-null   float64\n", " 12  Accounts_Payable     2500 non-null   float64\n", " 13  Equity               2500 non-null   float64\n", "dtypes: float64(11), int64(3)\n", "memory usage: 273.6 KB\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "Financial_Record_ID", "rawType": "float64", "type": "float"}, {"name": "SME_ID", "rawType": "float64", "type": "float"}, {"name": "Year", "rawType": "float64", "type": "float"}, {"name": "Revenue", "rawType": "float64", "type": "float"}, {"name": "COGS", "rawType": "float64", "type": "float"}, {"name": "Operating_Expense", "rawType": "float64", "type": "float"}, {"name": "Net_Income", "rawType": "float64", "type": "float"}, {"name": "Total_Assets", "rawType": "float64", "type": "float"}, {"name": "Cash_Holdings", "rawType": "float64", "type": "float"}, {"name": "Accounts_Receivable", "rawType": "float64", "type": "float"}, {"name": "Inventory", "rawType": "float64", "type": "float"}, {"name": "Total_Liabilities", "rawType": "float64", "type": "float"}, {"name": "Accounts_Payable", "rawType": "float64", "type": "float"}, {"name": "Equity", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "e9e801a4-7c54-4df3-ba92-ff0608ed81c6", "rows": [["count", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0", "2500.0"], ["mean", "1250.5", "250.5", "2020.0", "2642826.*********", "2342082.**********", "520132.***********", "-219388.**********", "3295569.**********", "412374.***********", "261869.***********", "464644.**********", "1644764.**********", "236060.***********", "1650804.**********"], ["std", "721.*************", "144.**************", "1.****************", "1480845.**********", "1331423.**********", "340632.**********", "301717.**********", "2265230.*********", "336149.***********", "172199.***********", "305114.**********", "1202074.**********", "155193.***********", "1244261.**********"], ["min", "1.0", "1.0", "2018.0", "99243.********404", "89082.63262994544", "12547.34520570282", "-1769606.52295343", "64288.06737265669", "4190.201891784112", "5179.625390059208", "11906.846891269117", "33140.32916854148", "5551.290988926961", "28489.557018992797"], ["25%", "625.75", "125.75", "2019.0", "1371244.2095613508", "1218496.8501208303", "241294.0447911181", "-373151.6169516907", "1482737.4054165226", "157144.01213444874", "119359.50997452125", "214501.950328993", "732018.8868513067", "109337.22629478568", "641814.0233780132"], ["50%", "1250.5", "250.5", "2020.0", "2725264.2887856965", "2417236.1187927257", "477747.37821839127", "-148355.09171638679", "2913278.64611423", "327431.94502014236", "239767.55470147464", "425753.5961092444", "1378665.7738652169", "217784.24591257688", "1401974.3239518576"], ["75%", "1875.25", "375.25", "2021.0", "3873749.0101300543", "3366407.980393432", "745972.2091712495", "-22747.31890582323", "4826477.161784524", "568566.0379371315", "375446.264768669", "671252.0697467817", "2298455.4542862773", "338457.87741739774", "2360350.4852912696"], ["max", "2500.0", "500.0", "2022.0", "6356573.360747807", "5897863.162336616", "1798429.7863455566", "735960.6323716567", "11059411.698129088", "1855607.3624950356", "913283.7913139611", "1644821.4103088663", "6399647.5563789485", "815082.5525240034", "6452708.150854324"]], "shape": {"columns": 14, "rows": 8}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Financial_Record_ID</th>\n", "      <th>SME_ID</th>\n", "      <th>Year</th>\n", "      <th>Revenue</th>\n", "      <th>COGS</th>\n", "      <th>Operating_Expense</th>\n", "      <th>Net_Income</th>\n", "      <th>Total_Assets</th>\n", "      <th>Cash_Holdings</th>\n", "      <th>Accounts_Receivable</th>\n", "      <th>Inventory</th>\n", "      <th>Total_Liabilities</th>\n", "      <th>Accounts_Payable</th>\n", "      <th>Equity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2500.00000</td>\n", "      <td>2500.000000</td>\n", "      <td>2500.000000</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2500.000000</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2500.000000</td>\n", "      <td>2.500000e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1250.50000</td>\n", "      <td>250.500000</td>\n", "      <td>2020.000000</td>\n", "      <td>2.642827e+06</td>\n", "      <td>2.342083e+06</td>\n", "      <td>5.201325e+05</td>\n", "      <td>-2.193889e+05</td>\n", "      <td>3.295569e+06</td>\n", "      <td>4.123748e+05</td>\n", "      <td>261869.751889</td>\n", "      <td>4.646442e+05</td>\n", "      <td>1.644764e+06</td>\n", "      <td>236060.531579</td>\n", "      <td>1.650805e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>721.83216</td>\n", "      <td>144.366155</td>\n", "      <td>1.414496</td>\n", "      <td>1.480846e+06</td>\n", "      <td>1.331424e+06</td>\n", "      <td>3.406322e+05</td>\n", "      <td>3.017171e+05</td>\n", "      <td>2.265231e+06</td>\n", "      <td>3.361491e+05</td>\n", "      <td>172199.177376</td>\n", "      <td>3.051149e+05</td>\n", "      <td>1.202075e+06</td>\n", "      <td>155193.702017</td>\n", "      <td>1.244262e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.00000</td>\n", "      <td>1.000000</td>\n", "      <td>2018.000000</td>\n", "      <td>9.924303e+04</td>\n", "      <td>8.908263e+04</td>\n", "      <td>1.254735e+04</td>\n", "      <td>-1.769607e+06</td>\n", "      <td>6.428807e+04</td>\n", "      <td>4.190202e+03</td>\n", "      <td>5179.625390</td>\n", "      <td>1.190685e+04</td>\n", "      <td>3.314033e+04</td>\n", "      <td>5551.290989</td>\n", "      <td>2.848956e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>625.75000</td>\n", "      <td>125.750000</td>\n", "      <td>2019.000000</td>\n", "      <td>1.371244e+06</td>\n", "      <td>1.218497e+06</td>\n", "      <td>2.412940e+05</td>\n", "      <td>-3.731516e+05</td>\n", "      <td>1.482737e+06</td>\n", "      <td>1.571440e+05</td>\n", "      <td>119359.509975</td>\n", "      <td>2.145020e+05</td>\n", "      <td>7.320189e+05</td>\n", "      <td>109337.226295</td>\n", "      <td>6.418140e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1250.50000</td>\n", "      <td>250.500000</td>\n", "      <td>2020.000000</td>\n", "      <td>2.725264e+06</td>\n", "      <td>2.417236e+06</td>\n", "      <td>4.777474e+05</td>\n", "      <td>-1.483551e+05</td>\n", "      <td>2.913279e+06</td>\n", "      <td>3.274319e+05</td>\n", "      <td>239767.554701</td>\n", "      <td>4.257536e+05</td>\n", "      <td>1.378666e+06</td>\n", "      <td>217784.245913</td>\n", "      <td>1.401974e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1875.25000</td>\n", "      <td>375.250000</td>\n", "      <td>2021.000000</td>\n", "      <td>3.873749e+06</td>\n", "      <td>3.366408e+06</td>\n", "      <td>7.459722e+05</td>\n", "      <td>-2.274732e+04</td>\n", "      <td>4.826477e+06</td>\n", "      <td>5.685660e+05</td>\n", "      <td>375446.264769</td>\n", "      <td>6.712521e+05</td>\n", "      <td>2.298455e+06</td>\n", "      <td>338457.877417</td>\n", "      <td>2.360350e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>2500.00000</td>\n", "      <td>500.000000</td>\n", "      <td>2022.000000</td>\n", "      <td>6.356573e+06</td>\n", "      <td>5.897863e+06</td>\n", "      <td>1.798430e+06</td>\n", "      <td>7.359606e+05</td>\n", "      <td>1.105941e+07</td>\n", "      <td>1.855607e+06</td>\n", "      <td>913283.791314</td>\n", "      <td>1.644821e+06</td>\n", "      <td>6.399648e+06</td>\n", "      <td>815082.552524</td>\n", "      <td>6.452708e+06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Financial_Record_ID       SME_ID         Year       Revenue  \\\n", "count           2500.00000  2500.000000  2500.000000  2.500000e+03   \n", "mean            1250.50000   250.500000  2020.000000  2.642827e+06   \n", "std              721.83216   144.366155     1.414496  1.480846e+06   \n", "min                1.00000     1.000000  2018.000000  9.924303e+04   \n", "25%              625.75000   125.750000  2019.000000  1.371244e+06   \n", "50%             1250.50000   250.500000  2020.000000  2.725264e+06   \n", "75%             1875.25000   375.250000  2021.000000  3.873749e+06   \n", "max             2500.00000   500.000000  2022.000000  6.356573e+06   \n", "\n", "               COGS  Operating_Expense    Net_Income  Total_Assets  \\\n", "count  2.500000e+03       2.500000e+03  2.500000e+03  2.500000e+03   \n", "mean   2.342083e+06       5.201325e+05 -2.193889e+05  3.295569e+06   \n", "std    1.331424e+06       3.406322e+05  3.017171e+05  2.265231e+06   \n", "min    8.908263e+04       1.254735e+04 -1.769607e+06  6.428807e+04   \n", "25%    1.218497e+06       2.412940e+05 -3.731516e+05  1.482737e+06   \n", "50%    2.417236e+06       4.777474e+05 -1.483551e+05  2.913279e+06   \n", "75%    3.366408e+06       7.459722e+05 -2.274732e+04  4.826477e+06   \n", "max    5.897863e+06       1.798430e+06  7.359606e+05  1.105941e+07   \n", "\n", "       Cash_Holdings  Accounts_Receivable     Inventory  Total_Liabilities  \\\n", "count   2.500000e+03          2500.000000  2.500000e+03       2.500000e+03   \n", "mean    4.123748e+05        261869.751889  4.646442e+05       1.644764e+06   \n", "std     3.361491e+05        172199.177376  3.051149e+05       1.202075e+06   \n", "min     4.190202e+03          5179.625390  1.190685e+04       3.314033e+04   \n", "25%     1.571440e+05        119359.509975  2.145020e+05       7.320189e+05   \n", "50%     3.274319e+05        239767.554701  4.257536e+05       1.378666e+06   \n", "75%     5.685660e+05        375446.264769  6.712521e+05       2.298455e+06   \n", "max     1.855607e+06        913283.791314  1.644821e+06       6.399648e+06   \n", "\n", "       Accounts_Payable        Equity  \n", "count       2500.000000  2.500000e+03  \n", "mean      236060.531579  1.650805e+06  \n", "std       155193.702017  1.244262e+06  \n", "min         5551.290989  2.848956e+04  \n", "25%       109337.226295  6.418140e+05  \n", "50%       217784.245913  1.401974e+06  \n", "75%       338457.877417  2.360350e+06  \n", "max       815082.552524  6.452708e+06  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "from faker import Faker\n", "import random\n", "from datetime import datetime, timedelta\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "random.seed(42)\n", "fake = Faker()\n", "Faker.seed(42)\n", "\n", "# Constants\n", "NUM_SMES = 500\n", "YEARS = [2018, 2019, 2020, 2021, 2022]\n", "INDUSTRY_SECTORS = ['Retail', 'Manufacturing', 'Technology', 'Services', 'Healthcare', 'Construction']\n", "LOCATIONS = ['Urban-Tier1', 'Urban-Tier2', 'Rural-Tier1']\n", "DEFAULT_RATE = 0.15  # 15% default rate\n", "\n", "# Helper functions for data generation\n", "def generate_sme_profile():\n", "    \"\"\"Generate basic SME profile data\"\"\"\n", "    profiles = []\n", "    \n", "    for sme_id in range(1, NUM_SMES + 1):\n", "        years_in_business = random.randint(1, 20)\n", "        industry = random.choice(INDUSTRY_SECTORS)\n", "        location = random.choice(LOCATIONS)\n", "        location_risk = 1 if location == 'Urban-Tier1' else (2 if location == 'Urban-Tier2' else 3)\n", "        \n", "        profiles.append({\n", "            'SME_ID': sme_id,\n", "            'SME_Name': fake.company(),\n", "            'Industry_Sector': industry,\n", "            'Years_In_Business': years_in_business,\n", "            'Geographic_Location': location,\n", "            'Geographic_Location_Risk_Tier': location_risk\n", "        })\n", "    \n", "    return pd.DataFrame(profiles)\n", "\n", "# Generate SME profiles\n", "sme_profiles = generate_sme_profile()\n", "sme_profiles.head()\n", "\n", "# Generate financial data for each SME\n", "\n", "def generate_financial_data():\n", "    \"\"\"Generate yearly financial data for each SME\"\"\"\n", "    financial_data = []\n", "    \n", "    for sme_id in range(1, NUM_SMES + 1):\n", "        # Base financial metrics that will grow/shrink over years\n", "        base_revenue = random.uniform(100000, 5000000)  # Base revenue between $100K and $5M\n", "        base_assets = base_revenue * random.uniform(0.5, 2.0)  # Assets as a multiple of revenue\n", "        base_equity = base_assets * random.uniform(0.3, 0.7)  # Equity as a percentage of assets\n", "        base_liabilities = base_assets - base_equity  # Liabilities = Assets - Equity\n", "        \n", "        # Get SME profile for industry-specific adjustments\n", "        sme_profile = sme_profiles[sme_profiles['SME_ID'] == sme_id].iloc[0]\n", "        industry = sme_profile['Industry_Sector']\n", "        years_in_business = sme_profile['Years_In_Business']\n", "        \n", "        # Industry-specific adjustments\n", "        industry_factors = {\n", "            'Retail': {'revenue_volatility': 0.2, 'profit_margin': 0.05},\n", "            'Manufacturing': {'revenue_volatility': 0.15, 'profit_margin': 0.12},\n", "            'Technology': {'revenue_volatility': 0.3, 'profit_margin': 0.15},\n", "            'Services': {'revenue_volatility': 0.1, 'profit_margin': 0.2},\n", "            'Healthcare': {'revenue_volatility': 0.05, 'profit_margin': 0.1},\n", "            'Construction': {'revenue_volatility': 0.25, 'profit_margin': 0.08}\n", "        }\n", "        \n", "        volatility = industry_factors[industry]['revenue_volatility']\n", "        profit_margin = industry_factors[industry]['profit_margin']\n", "        \n", "        # Generate data for each year\n", "        for year in YEARS:\n", "            # Apply random growth/decline trend with industry volatility\n", "            year_factor = 1 + random.uniform(-volatility, volatility)\n", "            \n", "            # Calculate financial metrics for this year\n", "            revenue = base_revenue * year_factor\n", "            cogs = revenue * (1 - profit_margin) * random.uniform(0.9, 1.1)  # Cost of goods sold\n", "            operating_expense = revenue * random.uniform(0.1, 0.3)  # Operating expenses\n", "            net_income = revenue - cogs - operating_expense\n", "            \n", "            # Balance sheet items\n", "            assets = base_assets * year_factor\n", "            cash = assets * random.uniform(0.05, 0.2)\n", "            accounts_receivable = revenue * random.uniform(0.05, 0.15)\n", "            inventory = cogs * random.uniform(0.1, 0.3)\n", "            \n", "            liabilities = base_liabilities * year_factor\n", "            accounts_payable = cogs * random.uniform(0.05, 0.15)\n", "            \n", "            equity = assets - liabilities\n", "            \n", "            financial_data.append({\n", "                'Financial_Record_ID': len(financial_data) + 1,\n", "                'SME_ID': sme_id,\n", "                'Year': year,\n", "                'Revenue': revenue,\n", "                'COGS': cogs,\n", "                'Operating_Expense': operating_expense,\n", "                'Net_Income': net_income,\n", "                'Total_Assets': assets,\n", "                'Cash_Holdings': cash,\n", "                'Accounts_Receivable': accounts_receivable,\n", "                'Inventory': inventory,\n", "                'Total_Liabilities': liabilities,\n", "                'Accounts_Payable': accounts_payable,\n", "                'Equity': equity\n", "            })\n", "    \n", "    return pd.DataFrame(financial_data)\n", "\n", "# Generate financial data\n", "sme_financials = generate_financial_data()\n", "sme_financials.head()\n", "sme_financials.info()\n", "sme_financials.describe()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b047cc5c", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Alt_Data_ID", "rawType": "int64", "type": "integer"}, {"name": "SME_ID", "rawType": "int64", "type": "integer"}, {"name": "Online_Presence_Score", "rawType": "float64", "type": "float"}, {"name": "Average_Customer_Review_Score", "rawType": "float64", "type": "float"}, {"name": "Number_of_Customer_Complaints_Monthly", "rawType": "int64", "type": "integer"}, {"name": "Payment_Gateway_Transaction_Volatility", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "8724b385-c1a2-4209-b282-d2835f2a0776", "rows": [["0", "1", "1", "5.000392270621811", "3.744802121951845", "7", "0.23916814745525294"], ["1", "2", "2", "4.1253304813061655", "4.232810532361225", "3", "0.23435934346282583"], ["2", "3", "3", "2.4414353006576803", "3.596547802048535", "8", "0.1869345111338247"], ["3", "4", "4", "3.80634978786186", "4.024691074517916", "5", "0.22443661367442413"], ["4", "5", "5", "10.0", "3.6630885641896813", "5", "0.2089412196198009"]], "shape": {"columns": 6, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Alt_Data_ID</th>\n", "      <th>SME_ID</th>\n", "      <th>Online_Presence_Score</th>\n", "      <th>Average_Customer_Review_Score</th>\n", "      <th>Number_of_Customer_Complaints_Monthly</th>\n", "      <th>Payment_Gateway_Transaction_Volatility</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5.000392</td>\n", "      <td>3.744802</td>\n", "      <td>7</td>\n", "      <td>0.239168</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>4.125330</td>\n", "      <td>4.232811</td>\n", "      <td>3</td>\n", "      <td>0.234359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2.441435</td>\n", "      <td>3.596548</td>\n", "      <td>8</td>\n", "      <td>0.186935</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>3.806350</td>\n", "      <td>4.024691</td>\n", "      <td>5</td>\n", "      <td>0.224437</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>10.000000</td>\n", "      <td>3.663089</td>\n", "      <td>5</td>\n", "      <td>0.208941</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Alt_Data_ID  SME_ID  Online_Presence_Score  Average_Customer_Review_Score  \\\n", "0            1       1               5.000392                       3.744802   \n", "1            2       2               4.125330                       4.232811   \n", "2            3       3               2.441435                       3.596548   \n", "3            4       4               3.806350                       4.024691   \n", "4            5       5              10.000000                       3.663089   \n", "\n", "   Number_of_Customer_Complaints_Monthly  \\\n", "0                                      7   \n", "1                                      3   \n", "2                                      8   \n", "3                                      5   \n", "4                                      5   \n", "\n", "   Payment_Gateway_Transaction_Volatility  \n", "0                                0.239168  \n", "1                                0.234359  \n", "2                                0.186935  \n", "3                                0.224437  \n", "4                                0.208941  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def generate_alternative_data():\n", "    \"\"\"Generate alternative data for each SME\"\"\"\n", "    alternative_data = []\n", "    \n", "    for sme_id in range(1, NUM_SMES + 1):\n", "        # Get SME profile for industry and location adjustments\n", "        sme_profile = sme_profiles[sme_profiles['SME_ID'] == sme_id].iloc[0]\n", "        industry = sme_profile['Industry_Sector']\n", "        location = sme_profile['Geographic_Location']\n", "        years_in_business = sme_profile['Years_In_Business']\n", "        \n", "        # Base scores adjusted by industry and location\n", "        # Online presence tends to be higher for tech, lower for construction\n", "        industry_online_factor = {\n", "            'Technology': 2.0, 'Retail': 1.5, 'Services': 1.2,\n", "            'Healthcare': 1.0, 'Manufacturing': 0.8, 'Construction': 0.6\n", "        }\n", "        \n", "        # Urban locations tend to have better online presence\n", "        location_online_factor = {\n", "            'Urban-Tier1': 1.3, 'Urban-Tier2': 1.0, 'Rural-Tier1': 0.7\n", "        }\n", "        \n", "        # Calculate online presence score (0-10)\n", "        base_online_score = random.uniform(3, 7)\n", "        online_presence_score = min(10, base_online_score * \n", "                                  industry_online_factor[industry] * \n", "                                  location_online_factor[location])\n", "        \n", "        # Customer reviews tend to correlate somewhat with years in business\n", "        experience_factor = min(1.0, years_in_business / 10)  # Caps at 10 years\n", "        avg_review_score = min(5.0, random.uniform(2.5, 4.0) + experience_factor)\n", "        \n", "        # Customer complaints - inversely related to review score but with randomness\n", "        monthly_complaints = max(0, int(random.uniform(0, 20) * (6 - avg_review_score) / 5))\n", "        \n", "        # Payment volatility - higher for newer businesses and certain industries\n", "        volatility_factor = (1 + (10 - min(10, years_in_business)) / 10)\n", "        industry_volatility = {\n", "            'Retail': 1.3, 'Construction': 1.4, 'Manufacturing': 1.1,\n", "            'Technology': 1.2, 'Services': 0.9, 'Healthcare': 0.7\n", "        }\n", "        payment_volatility = random.uniform(0.05, 0.2) * volatility_factor * industry_volatility[industry]\n", "        \n", "        alternative_data.append({\n", "            'Alt_Data_ID': len(alternative_data) + 1,\n", "            'SME_ID': sme_id,\n", "            'Online_Presence_Score': online_presence_score,\n", "            'Average_Customer_Review_Score': avg_review_score,\n", "            'Number_of_Customer_Complaints_Monthly': monthly_complaints,\n", "            'Payment_Gateway_Transaction_Volatility': payment_volatility\n", "        })\n", "    \n", "    return pd.DataFrame(alternative_data)\n", "\n", "# Generate alternative data\n", "sme_alternative_data = generate_alternative_data()\n", "sme_alternative_data.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "9759bf0b", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Loan_Application_ID", "rawType": "int64", "type": "integer"}, {"name": "SME_ID", "rawType": "int64", "type": "integer"}, {"name": "Loan_Application_Date", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "Loan_Amount_Requested", "rawType": "float64", "type": "float"}, {"name": "Default_Probability", "rawType": "float64", "type": "float"}, {"name": "Loan_Default_Status", "rawType": "int64", "type": "integer"}, {"name": "Observation_EndDate", "rawType": "datetime64[ns]", "type": "datetime"}], "conversionMethod": "pd.DataFrame", "ref": "8ccb5bf2-2e35-44fa-85bb-55d215116be4", "rows": [["0", "1", "1", "2020-12-02 00:00:00", "309025.94451456337", "0.6152457713590871", "1", "2022-12-02 00:00:00"], ["1", "2", "1", "2021-12-22 00:00:00", "486330.5839928837", "0.05", "0", "2023-12-22 00:00:00"], ["2", "3", "2", "2020-05-03 00:00:00", "1088745.2776361045", "0.5103022534106548", "1", "2022-05-03 00:00:00"], ["3", "4", "3", "2021-11-24 00:00:00", "294347.4115063075", "0.41626783176041154", "0", "2023-11-24 00:00:00"], ["4", "5", "3", "2022-01-15 00:00:00", "932082.423703843", "0.48970058459982646", "0", "2024-01-15 00:00:00"]], "shape": {"columns": 7, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Loan_Application_ID</th>\n", "      <th>SME_ID</th>\n", "      <th>Loan_Application_Date</th>\n", "      <th>Loan_Amount_Requested</th>\n", "      <th>Default_Probability</th>\n", "      <th><PERSON><PERSON>_De<PERSON>ult_Status</th>\n", "      <th>Observation_EndDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2020-12-02</td>\n", "      <td>3.090259e+05</td>\n", "      <td>0.615246</td>\n", "      <td>1</td>\n", "      <td>2022-12-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>2021-12-22</td>\n", "      <td>4.863306e+05</td>\n", "      <td>0.050000</td>\n", "      <td>0</td>\n", "      <td>2023-12-22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>2020-05-03</td>\n", "      <td>1.088745e+06</td>\n", "      <td>0.510302</td>\n", "      <td>1</td>\n", "      <td>2022-05-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>2021-11-24</td>\n", "      <td>2.943474e+05</td>\n", "      <td>0.416268</td>\n", "      <td>0</td>\n", "      <td>2023-11-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>2022-01-15</td>\n", "      <td>9.320824e+05</td>\n", "      <td>0.489701</td>\n", "      <td>0</td>\n", "      <td>2024-01-15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Loan_Application_ID  SME_ID Loan_Application_Date  Loan_Amount_Requested  \\\n", "0                    1       1            2020-12-02           3.090259e+05   \n", "1                    2       1            2021-12-22           4.863306e+05   \n", "2                    3       2            2020-05-03           1.088745e+06   \n", "3                    4       3            2021-11-24           2.943474e+05   \n", "4                    5       3            2022-01-15           9.320824e+05   \n", "\n", "   Default_Probability  Loan_Default_Status Observation_EndDate  \n", "0             0.615246                    1          2022-12-02  \n", "1             0.050000                    0          2023-12-22  \n", "2             0.510302                    1          2022-05-03  \n", "3             0.416268                    0          2023-11-24  \n", "4             0.489701                    0          2024-01-15  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["def generate_loan_applications():\n", "    \"\"\"Generate loan application data with default status\"\"\"\n", "    loan_applications = []\n", "    \n", "    for sme_id in range(1, NUM_SMES + 1):\n", "        # Get SME profile and financial data\n", "        sme_profile = sme_profiles[sme_profiles['SME_ID'] == sme_id].iloc[0]\n", "        sme_financials_subset = sme_financials[sme_financials['SME_ID'] == sme_id]\n", "        alt_data = sme_alternative_data[sme_alternative_data['SME_ID'] == sme_id].iloc[0]\n", "        \n", "        # Determine number of loan applications (1-3)\n", "        num_applications = random.randint(1, 3)\n", "        \n", "        for i in range(num_applications):\n", "            # Random application date between 2019-2022\n", "            application_year = random.choice([2019, 2020, 2021, 2022])\n", "            application_month = random.randint(1, 12)\n", "            application_day = random.randint(1, 28)  # Avoid month end issues\n", "            application_date = datetime(application_year, application_month, application_day)\n", "            \n", "            # Get financial data from the year before application\n", "            prev_year = application_year - 1\n", "            prev_year_financials = sme_financials_subset[sme_financials_subset['Year'] == prev_year]\n", "            \n", "            if len(prev_year_financials) == 0:\n", "                continue  # Skip if no financial data for previous year\n", "            \n", "            prev_year_financials = prev_year_financials.iloc[0]\n", "            \n", "            # Calculate loan amount based on revenue\n", "            revenue = prev_year_financials['Revenue']\n", "            loan_amount = random.uniform(0.1, 0.5) * revenue\n", "            \n", "            # Calculate default probability based on financial and alternative data\n", "            # Financial factors\n", "            debt_to_equity = prev_year_financials['Total_Liabilities'] / max(1, prev_year_financials['Equity'])\n", "            profit_margin = prev_year_financials['Net_Income'] / max(1, prev_year_financials['Revenue'])\n", "            liquidity = prev_year_financials['Cash_Holdings'] / max(1, prev_year_financials['Accounts_Payable'])\n", "            \n", "            # Alternative data factors\n", "            online_score = alt_data['Online_Presence_Score'] / 10  # Normalize to 0-1\n", "            review_score = alt_data['Average_Customer_Review_Score'] / 5  # Normalize to 0-1\n", "            complaint_factor = min(1, alt_data['Number_of_Customer_Complaints_Monthly'] / 20)  # Higher is worse\n", "            volatility = alt_data['Payment_Gateway_Transaction_Volatility']  # Higher is worse\n", "            \n", "            # Location and industry risk\n", "            location_risk = sme_profile['Geographic_Location_Risk_Tier'] / 3  # Normalize to 0-1\n", "            industry_risk = {\n", "                'Retail': 0.6, 'Manufacturing': 0.5, 'Technology': 0.4,\n", "                'Services': 0.5, 'Healthcare': 0.3, 'Construction': 0.7\n", "            }[sme_profile['Industry_Sector']]\n", "            \n", "            # Calculate default probability (higher is worse)\n", "            default_prob = (\n", "                0.3 * (debt_to_equity / 3) +  # Higher debt/equity increases default risk\n", "                0.2 * (1 - profit_margin) +   # Lower profit margin increases default risk\n", "                0.1 * (1 - liquidity) +       # Lower liquidity increases default risk\n", "                0.1 * (1 - online_score) +    # Lower online presence increases default risk\n", "                0.1 * (1 - review_score) +    # Lower reviews increases default risk\n", "                0.05 * complaint_factor +     # More complaints increases default risk\n", "                0.05 * volatility +           # Higher volatility increases default risk\n", "                0.05 * location_risk +        # Higher location risk increases default risk\n", "                0.05 * industry_risk          # Industry-specific risk\n", "            )\n", "            \n", "            # Add some randomness\n", "            default_prob = min(0.95, max(0.05, default_prob + random.uniform(-0.1, 0.1)))\n", "            \n", "            # Determine default status based on probability\n", "            default_status = 1 if random.random() < default_prob else 0\n", "            \n", "            # Observation end date (2 years after application)\n", "            observation_end = application_date + <PERSON><PERSON><PERSON>(days=730)\n", "            \n", "            loan_applications.append({\n", "                'Loan_Application_ID': len(loan_applications) + 1,\n", "                'SME_ID': sme_id,\n", "                'Loan_Application_Date': application_date,\n", "                'Loan_Amount_Requested': loan_amount,\n", "                'Default_Probability': default_prob,  # For reference, would not be known in real scenario\n", "                'Loan_Default_Status': default_status,\n", "                'Observation_EndDate': observation_end\n", "            })\n", "    \n", "    return pd.DataFrame(loan_applications)\n", "\n", "# Generate loan applications\n", "sme_loan_applications = generate_loan_applications()\n", "sme_loan_applications.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "c2e9bc88", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All datasets have been saved to the 'data' directory.\n", "\n", "Total SMEs: 500\n", "Total financial records: 2500\n", "Total alternative data records: 500\n", "Total loan applications: 991\n", "Default rate: 38.35%\n"]}], "source": ["# Create data directory if it doesn't exist\n", "import os\n", "if not os.path.exists('data'):\n", "    os.makedirs('data')\n", "\n", "# Save datasets to CSV files\n", "sme_profiles.to_csv('data/sme_profiles.csv', index=False)\n", "sme_financials.to_csv('data/sme_financials.csv', index=False)\n", "sme_alternative_data.to_csv('data/sme_alternative_data.csv', index=False)\n", "sme_loan_applications.to_csv('data/sme_loan_applications.csv', index=False)\n", "\n", "print(\"All datasets have been saved to the 'data' directory.\")\n", "\n", "# Display summary statistics\n", "print(f\"\\nTotal SMEs: {len(sme_profiles)}\")\n", "print(f\"Total financial records: {len(sme_financials)}\")\n", "print(f\"Total alternative data records: {len(sme_alternative_data)}\")\n", "print(f\"Total loan applications: {len(sme_loan_applications)}\")\n", "print(f\"Default rate: {sme_loan_applications['Loan_Default_Status'].mean():.2%}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}