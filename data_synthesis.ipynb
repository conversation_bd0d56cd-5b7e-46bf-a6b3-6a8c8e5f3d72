import pandas as pd
import numpy as np
from faker import Faker
import random
from datetime import datetime, timedelta

# Set random seed for reproducibility
np.random.seed(42)
random.seed(42)
fake = Faker()
Faker.seed(42)

# Constants
NUM_SMES = 500
YEARS = [2018, 2019, 2020, 2021, 2022]
INDUSTRY_SECTORS = ['Retail', 'Manufacturing', 'Technology', 'Services', 'Healthcare', 'Construction']
LOCATIONS = ['Urban-Tier1', 'Urban-Tier2', 'Rural-Tier1']
DEFAULT_RATE = 0.15  # 15% default rate

# Helper functions for data generation
def generate_sme_profile():
    """Generate basic SME profile data"""
    profiles = []
    
    for sme_id in range(1, NUM_SMES + 1):
        years_in_business = random.randint(1, 20)
        industry = random.choice(INDUSTRY_SECTORS)
        location = random.choice(LOCATIONS)
        location_risk = 1 if location == 'Urban-Tier1' else (2 if location == 'Urban-Tier2' else 3)
        
        profiles.append({
            'SME_ID': sme_id,
            'SME_Name': fake.company(),
            'Industry_Sector': industry,
            'Years_In_Business': years_in_business,
            'Geographic_Location': location,
            'Geographic_Location_Risk_Tier': location_risk
        })
    
    return pd.DataFrame(profiles)

# Generate SME profiles
sme_profiles = generate_sme_profile()
sme_profiles.head()

# Generate financial data for each SME

def generate_financial_data():
    """Generate yearly financial data for each SME"""
    financial_data = []
    
    for sme_id in range(1, NUM_SMES + 1):
        # Base financial metrics that will grow/shrink over years
        base_revenue = random.uniform(100000, 5000000)  # Base revenue between $100K and $5M
        base_assets = base_revenue * random.uniform(0.5, 2.0)  # Assets as a multiple of revenue
        base_equity = base_assets * random.uniform(0.3, 0.7)  # Equity as a percentage of assets
        base_liabilities = base_assets - base_equity  # Liabilities = Assets - Equity
        
        # Get SME profile for industry-specific adjustments
        sme_profile = sme_profiles[sme_profiles['SME_ID'] == sme_id].iloc[0]
        industry = sme_profile['Industry_Sector']
        years_in_business = sme_profile['Years_In_Business']
        
        # Industry-specific adjustments
        industry_factors = {
            'Retail': {'revenue_volatility': 0.2, 'profit_margin': 0.05},
            'Manufacturing': {'revenue_volatility': 0.15, 'profit_margin': 0.12},
            'Technology': {'revenue_volatility': 0.3, 'profit_margin': 0.15},
            'Services': {'revenue_volatility': 0.1, 'profit_margin': 0.2},
            'Healthcare': {'revenue_volatility': 0.05, 'profit_margin': 0.1},
            'Construction': {'revenue_volatility': 0.25, 'profit_margin': 0.08}
        }
        
        volatility = industry_factors[industry]['revenue_volatility']
        profit_margin = industry_factors[industry]['profit_margin']
        
        # Generate data for each year
        for year in YEARS:
            # Apply random growth/decline trend with industry volatility
            year_factor = 1 + random.uniform(-volatility, volatility)
            
            # Calculate financial metrics for this year
            revenue = base_revenue * year_factor
            cogs = revenue * (1 - profit_margin) * random.uniform(0.9, 1.1)  # Cost of goods sold
            operating_expense = revenue * random.uniform(0.1, 0.3)  # Operating expenses
            net_income = revenue - cogs - operating_expense
            
            # Balance sheet items
            assets = base_assets * year_factor
            cash = assets * random.uniform(0.05, 0.2)
            accounts_receivable = revenue * random.uniform(0.05, 0.15)
            inventory = cogs * random.uniform(0.1, 0.3)
            
            liabilities = base_liabilities * year_factor
            accounts_payable = cogs * random.uniform(0.05, 0.15)
            
            equity = assets - liabilities
            
            financial_data.append({
                'Financial_Record_ID': len(financial_data) + 1,
                'SME_ID': sme_id,
                'Year': year,
                'Revenue': revenue,
                'COGS': cogs,
                'Operating_Expense': operating_expense,
                'Net_Income': net_income,
                'Total_Assets': assets,
                'Cash_Holdings': cash,
                'Accounts_Receivable': accounts_receivable,
                'Inventory': inventory,
                'Total_Liabilities': liabilities,
                'Accounts_Payable': accounts_payable,
                'Equity': equity
            })
    
    return pd.DataFrame(financial_data)

# Generate financial data
sme_financials = generate_financial_data()
sme_financials.head()
sme_financials.info()
sme_financials.describe()


def generate_alternative_data():
    """Generate alternative data for each SME"""
    alternative_data = []
    
    for sme_id in range(1, NUM_SMES + 1):
        # Get SME profile for industry and location adjustments
        sme_profile = sme_profiles[sme_profiles['SME_ID'] == sme_id].iloc[0]
        industry = sme_profile['Industry_Sector']
        location = sme_profile['Geographic_Location']
        years_in_business = sme_profile['Years_In_Business']
        
        # Base scores adjusted by industry and location
        # Online presence tends to be higher for tech, lower for construction
        industry_online_factor = {
            'Technology': 2.0, 'Retail': 1.5, 'Services': 1.2,
            'Healthcare': 1.0, 'Manufacturing': 0.8, 'Construction': 0.6
        }
        
        # Urban locations tend to have better online presence
        location_online_factor = {
            'Urban-Tier1': 1.3, 'Urban-Tier2': 1.0, 'Rural-Tier1': 0.7
        }
        
        # Calculate online presence score (0-10)
        base_online_score = random.uniform(3, 7)
        online_presence_score = min(10, base_online_score * 
                                  industry_online_factor[industry] * 
                                  location_online_factor[location])
        
        # Customer reviews tend to correlate somewhat with years in business
        experience_factor = min(1.0, years_in_business / 10)  # Caps at 10 years
        avg_review_score = min(5.0, random.uniform(2.5, 4.0) + experience_factor)
        
        # Customer complaints - inversely related to review score but with randomness
        monthly_complaints = max(0, int(random.uniform(0, 20) * (6 - avg_review_score) / 5))
        
        # Payment volatility - higher for newer businesses and certain industries
        volatility_factor = (1 + (10 - min(10, years_in_business)) / 10)
        industry_volatility = {
            'Retail': 1.3, 'Construction': 1.4, 'Manufacturing': 1.1,
            'Technology': 1.2, 'Services': 0.9, 'Healthcare': 0.7
        }
        payment_volatility = random.uniform(0.05, 0.2) * volatility_factor * industry_volatility[industry]
        
        alternative_data.append({
            'Alt_Data_ID': len(alternative_data) + 1,
            'SME_ID': sme_id,
            'Online_Presence_Score': online_presence_score,
            'Average_Customer_Review_Score': avg_review_score,
            'Number_of_Customer_Complaints_Monthly': monthly_complaints,
            'Payment_Gateway_Transaction_Volatility': payment_volatility
        })
    
    return pd.DataFrame(alternative_data)

# Generate alternative data
sme_alternative_data = generate_alternative_data()
sme_alternative_data.head()

def generate_loan_applications():
    """Generate loan application data with default status"""
    loan_applications = []
    
    for sme_id in range(1, NUM_SMES + 1):
        # Get SME profile and financial data
        sme_profile = sme_profiles[sme_profiles['SME_ID'] == sme_id].iloc[0]
        sme_financials_subset = sme_financials[sme_financials['SME_ID'] == sme_id]
        alt_data = sme_alternative_data[sme_alternative_data['SME_ID'] == sme_id].iloc[0]
        
        # Determine number of loan applications (1-3)
        num_applications = random.randint(1, 3)
        
        for i in range(num_applications):
            # Random application date between 2019-2022
            application_year = random.choice([2019, 2020, 2021, 2022])
            application_month = random.randint(1, 12)
            application_day = random.randint(1, 28)  # Avoid month end issues
            application_date = datetime(application_year, application_month, application_day)
            
            # Get financial data from the year before application
            prev_year = application_year - 1
            prev_year_financials = sme_financials_subset[sme_financials_subset['Year'] == prev_year]
            
            if len(prev_year_financials) == 0:
                continue  # Skip if no financial data for previous year
            
            prev_year_financials = prev_year_financials.iloc[0]
            
            # Calculate loan amount based on revenue
            revenue = prev_year_financials['Revenue']
            loan_amount = random.uniform(0.1, 0.5) * revenue
            
            # Calculate default probability based on financial and alternative data
            # Financial factors
            debt_to_equity = prev_year_financials['Total_Liabilities'] / max(1, prev_year_financials['Equity'])
            profit_margin = prev_year_financials['Net_Income'] / max(1, prev_year_financials['Revenue'])
            liquidity = prev_year_financials['Cash_Holdings'] / max(1, prev_year_financials['Accounts_Payable'])
            
            # Alternative data factors
            online_score = alt_data['Online_Presence_Score'] / 10  # Normalize to 0-1
            review_score = alt_data['Average_Customer_Review_Score'] / 5  # Normalize to 0-1
            complaint_factor = min(1, alt_data['Number_of_Customer_Complaints_Monthly'] / 20)  # Higher is worse
            volatility = alt_data['Payment_Gateway_Transaction_Volatility']  # Higher is worse
            
            # Location and industry risk
            location_risk = sme_profile['Geographic_Location_Risk_Tier'] / 3  # Normalize to 0-1
            industry_risk = {
                'Retail': 0.6, 'Manufacturing': 0.5, 'Technology': 0.4,
                'Services': 0.5, 'Healthcare': 0.3, 'Construction': 0.7
            }[sme_profile['Industry_Sector']]
            
            # Calculate default probability (higher is worse)
            default_prob = (
                0.3 * (debt_to_equity / 3) +  # Higher debt/equity increases default risk
                0.2 * (1 - profit_margin) +   # Lower profit margin increases default risk
                0.1 * (1 - liquidity) +       # Lower liquidity increases default risk
                0.1 * (1 - online_score) +    # Lower online presence increases default risk
                0.1 * (1 - review_score) +    # Lower reviews increases default risk
                0.05 * complaint_factor +     # More complaints increases default risk
                0.05 * volatility +           # Higher volatility increases default risk
                0.05 * location_risk +        # Higher location risk increases default risk
                0.05 * industry_risk          # Industry-specific risk
            )
            
            # Add some randomness
            default_prob = min(0.95, max(0.05, default_prob + random.uniform(-0.1, 0.1)))
            
            # Determine default status based on probability
            default_status = 1 if random.random() < default_prob else 0
            
            # Observation end date (2 years after application)
            observation_end = application_date + timedelta(days=730)
            
            loan_applications.append({
                'Loan_Application_ID': len(loan_applications) + 1,
                'SME_ID': sme_id,
                'Loan_Application_Date': application_date,
                'Loan_Amount_Requested': loan_amount,
                'Default_Probability': default_prob,  # For reference, would not be known in real scenario
                'Loan_Default_Status': default_status,
                'Observation_EndDate': observation_end
            })
    
    return pd.DataFrame(loan_applications)

# Generate loan applications
sme_loan_applications = generate_loan_applications()
sme_loan_applications.head()

# Create data directory if it doesn't exist
import os
if not os.path.exists('data'):
    os.makedirs('data')

# Save datasets to CSV files
sme_profiles.to_csv('data/sme_profiles.csv', index=False)
sme_financials.to_csv('data/sme_financials.csv', index=False)
sme_alternative_data.to_csv('data/sme_alternative_data.csv', index=False)
sme_loan_applications.to_csv('data/sme_loan_applications.csv', index=False)

print("All datasets have been saved to the 'data' directory.")

# Display summary statistics
print(f"\nTotal SMEs: {len(sme_profiles)}")
print(f"Total financial records: {len(sme_financials)}")
print(f"Total alternative data records: {len(sme_alternative_data)}")
print(f"Total loan applications: {len(sme_loan_applications)}")
print(f"Default rate: {sme_loan_applications['Loan_Default_Status'].mean():.2%}")