"""
Advanced Feature Engineering for SME Loan Default Prediction
This module contains sophisticated feature engineering techniques to improve model performance
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureEngineer:
    def __init__(self):
        self.scaler = StandardScaler()
        self.poly_features = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
        self.feature_selector = SelectKBest(score_func=f_classif)
        
    def create_financial_ratios(self, df):
        """Create comprehensive financial ratios"""
        print("Creating financial ratios...")
        
        # Profitability ratios
        df['Profit_Margin'] = df['Net_Income'] / df['Revenue']
        df['Return_on_Assets'] = df['Net_Income'] / df['Total_Assets']
        df['Return_on_Equity'] = df['Net_Income'] / df['Equity']
        df['Operating_Margin'] = (df['Revenue'] - df['Operating_Expense']) / df['Revenue']
        df['Gross_Margin'] = (df['Revenue'] - df['COGS']) / df['Revenue']
        
        # Liquidity ratios
        df['Current_Ratio'] = (df['Cash_Holdings'] + df['Accounts_Receivable']) / df['Accounts_Payable']
        df['Quick_Ratio'] = df['Cash_Holdings'] / df['Accounts_Payable']
        df['Cash_Ratio'] = df['Cash_Holdings'] / df['Total_Liabilities']
        df['Working_Capital'] = df['Cash_Holdings'] + df['Accounts_Receivable'] - df['Accounts_Payable']
        df['Working_Capital_Ratio'] = df['Working_Capital'] / df['Total_Assets']
        
        # Leverage ratios
        df['Debt_to_Equity'] = df['Total_Liabilities'] / df['Equity']
        df['Debt_to_Assets'] = df['Total_Liabilities'] / df['Total_Assets']
        df['Equity_Ratio'] = df['Equity'] / df['Total_Assets']
        df['Financial_Leverage'] = df['Total_Assets'] / df['Equity']
        df['Capitalization_Ratio'] = df['Total_Liabilities'] / (df['Total_Liabilities'] + df['Equity'])
        
        # Efficiency ratios
        df['Asset_Turnover'] = df['Revenue'] / df['Total_Assets']
        df['Inventory_Turnover'] = df['COGS'] / df['Inventory']
        df['Receivables_Turnover'] = df['Revenue'] / df['Accounts_Receivable']
        df['Payables_Turnover'] = df['COGS'] / df['Accounts_Payable']
        df['Equity_Turnover'] = df['Revenue'] / df['Equity']
        
        return df
    
    def create_advanced_metrics(self, df):
        """Create advanced financial health metrics"""
        print("Creating advanced metrics...")
        
        # DuPont Analysis components
        df['DuPont_ROE'] = df['Profit_Margin'] * df['Asset_Turnover'] * df['Financial_Leverage']
        
        # Altman Z-Score components (modified for SMEs)
        df['Working_Capital_to_Assets'] = df['Working_Capital'] / df['Total_Assets']
        df['Retained_Earnings_to_Assets'] = df['Net_Income'] / df['Total_Assets']  # Proxy
        df['EBIT_to_Assets'] = (df['Net_Income'] + df['Operating_Expense'] * 0.1) / df['Total_Assets']  # Proxy for EBIT
        df['Market_Value_to_Debt'] = df['Equity'] / df['Total_Liabilities']  # Book value proxy
        df['Sales_to_Assets'] = df['Revenue'] / df['Total_Assets']
        
        # Modified Altman Z-Score for SMEs
        df['Altman_Z_Score'] = (
            1.2 * df['Working_Capital_to_Assets'] +
            1.4 * df['Retained_Earnings_to_Assets'] +
            3.3 * df['EBIT_to_Assets'] +
            0.6 * df['Market_Value_to_Debt'] +
            1.0 * df['Sales_to_Assets']
        )
        
        # Cash conversion cycle
        df['Days_Sales_Outstanding'] = (df['Accounts_Receivable'] / df['Revenue']) * 365
        df['Days_Inventory_Outstanding'] = (df['Inventory'] / df['COGS']) * 365
        df['Days_Payable_Outstanding'] = (df['Accounts_Payable'] / df['COGS']) * 365
        df['Cash_Conversion_Cycle'] = df['Days_Sales_Outstanding'] + df['Days_Inventory_Outstanding'] - df['Days_Payable_Outstanding']
        
        # Interest coverage (estimated)
        df['Interest_Coverage'] = df['Net_Income'] / (df['Total_Liabilities'] * 0.05 + 0.01)  # Assuming 5% interest rate
        
        # Operating efficiency
        df['Operating_Efficiency'] = df['Revenue'] / df['Operating_Expense']
        df['Cost_Structure'] = df['COGS'] / df['Revenue']
        df['Expense_Ratio'] = df['Operating_Expense'] / df['Revenue']
        
        return df
    
    def create_risk_indicators(self, df):
        """Create risk-based indicators"""
        print("Creating risk indicators...")
        
        # Volatility measures (using coefficient of variation as proxy)
        df['Revenue_Stability'] = 1 / (abs(df['Profit_Margin']) + 0.01)  # Lower is more stable
        df['Liquidity_Risk'] = 1 / (df['Current_Ratio'] + 0.01)
        df['Leverage_Risk'] = df['Debt_to_Equity'] / (df['Debt_to_Equity'].quantile(0.75) + 0.01)
        
        # Business model indicators
        df['Asset_Intensity'] = df['Total_Assets'] / df['Revenue']
        df['Capital_Intensity'] = (df['Total_Assets'] - df['Cash_Holdings']) / df['Revenue']
        df['Labor_Intensity'] = df['Operating_Expense'] / df['Revenue']  # Proxy
        
        # Growth proxies (using cross-sectional data)
        df['Size_Percentile'] = df['Revenue'].rank(pct=True)
        df['Profitability_Percentile'] = df['Profit_Margin'].rank(pct=True)
        df['Efficiency_Percentile'] = df['Asset_Turnover'].rank(pct=True)
        
        return df
    
    def enhance_alternative_data(self, df):
        """Enhance alternative data features"""
        print("Enhancing alternative data...")
        
        # Digital presence indicators
        df['Digital_Maturity'] = (df['Online_Presence_Score'] / 10) * np.log1p(df['Years_In_Business'])
        df['Customer_Satisfaction_Index'] = (df['Average_Customer_Review_Score'] / 5) * (1 - df['Number_of_Customer_Complaints_Monthly'] / 20)
        df['Review_to_Complaints_Ratio'] = df['Average_Customer_Review_Score'] / (df['Number_of_Customer_Complaints_Monthly'] + 1)
        
        # Payment behavior indicators
        df['Payment_Reliability'] = 1 - df['Payment_Gateway_Transaction_Volatility']
        df['Digital_Trust_Score'] = (
            0.4 * (df['Online_Presence_Score'] / 10) +
            0.3 * (df['Average_Customer_Review_Score'] / 5) +
            0.2 * (1 - df['Number_of_Customer_Complaints_Monthly'] / 20) +
            0.1 * df['Payment_Reliability']
        )
        
        return df
    
    def create_interaction_features(self, df, top_features=None):
        """Create interaction features between important variables"""
        print("Creating interaction features...")
        
        if top_features is None:
            # Define key features for interactions
            top_features = [
                'Profit_Margin', 'Current_Ratio', 'Debt_to_Equity', 'Asset_Turnover',
                'Online_Presence_Score', 'Average_Customer_Review_Score', 'Years_In_Business'
            ]
        
        # Create specific meaningful interactions
        df['Profitability_x_Liquidity'] = df['Profit_Margin'] * df['Current_Ratio']
        df['Leverage_x_Profitability'] = df['Debt_to_Equity'] * df['Profit_Margin']
        df['Digital_x_Financial'] = df['Online_Presence_Score'] * df['Asset_Turnover']
        df['Experience_x_Performance'] = df['Years_In_Business'] * df['Return_on_Assets']
        df['Size_x_Efficiency'] = np.log1p(df['Revenue']) * df['Asset_Turnover']
        
        return df
    
    def create_industry_benchmarks(self, df):
        """Create industry-relative metrics"""
        print("Creating industry benchmarks...")
        
        # Group by industry and create relative metrics
        for industry in df['Industry_Sector'].unique():
            industry_mask = df['Industry_Sector'] == industry
            
            # Industry averages
            industry_profit_margin = df[industry_mask]['Profit_Margin'].median()
            industry_current_ratio = df[industry_mask]['Current_Ratio'].median()
            industry_debt_equity = df[industry_mask]['Debt_to_Equity'].median()
            
            # Relative performance
            df.loc[industry_mask, 'Profit_Margin_vs_Industry'] = df.loc[industry_mask, 'Profit_Margin'] / (industry_profit_margin + 0.01)
            df.loc[industry_mask, 'Current_Ratio_vs_Industry'] = df.loc[industry_mask, 'Current_Ratio'] / (industry_current_ratio + 0.01)
            df.loc[industry_mask, 'Debt_Equity_vs_Industry'] = df.loc[industry_mask, 'Debt_to_Equity'] / (industry_debt_equity + 0.01)
        
        return df
    
    def handle_outliers_and_missing(self, df):
        """Handle outliers and missing values"""
        print("Handling outliers and missing values...")
        
        # Replace infinite values
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # Cap extreme outliers at 99th percentile
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col not in ['SME_ID', 'Loan_Application_ID', 'Year', 'Loan_Default_Status']:
                q99 = df[col].quantile(0.99)
                q01 = df[col].quantile(0.01)
                df[col] = df[col].clip(lower=q01, upper=q99)
        
        # Fill missing values with median
        df = df.fillna(df.median())
        
        return df
    
    def engineer_all_features(self, df):
        """Apply all feature engineering techniques"""
        print("Starting comprehensive feature engineering...")
        
        # Apply all feature engineering steps
        df = self.create_financial_ratios(df)
        df = self.create_advanced_metrics(df)
        df = self.create_risk_indicators(df)
        df = self.enhance_alternative_data(df)
        df = self.create_interaction_features(df)
        df = self.create_industry_benchmarks(df)
        df = self.handle_outliers_and_missing(df)
        
        print(f"Feature engineering complete. Total features: {df.shape[1]}")
        return df

def main():
    """Test the feature engineering pipeline"""
    # This is for testing purposes
    print("Advanced Feature Engineering Module")
    print("Import this module to use the AdvancedFeatureEngineer class")

if __name__ == "__main__":
    main()
