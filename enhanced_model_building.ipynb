# Install required packages (run this cell first if packages are missing)
import subprocess
import sys

def install_package(package):
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print(f"✓ {package} installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"⚠ Warning: Could not install {package}. Error: {e}")

# Uncomment and run if you need to install packages
# install_package('xgboost>=1.5.0')
# install_package('lightgbm>=3.3.0')
# install_package('optuna>=2.10.0')
# install_package('plotly>=5.0.0')

# Core imports
import pandas as pd
import numpy as np
import warnings
import os
from datetime import datetime
import joblib

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Scikit-learn imports
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.metrics import (
    accuracy_score, classification_report, roc_auc_score, 
    confusion_matrix, roc_curve, precision_recall_curve
)

# Models
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import (
    RandomForestClassifier, GradientBoostingClassifier, 
    ExtraTreesClassifier, VotingClassifier, StackingClassifier
)
from sklearn.svm import SVC

# Imbalanced learning
from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
from imblearn.combine import SMOTEENN

# Advanced models (optional)
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
    print("✓ XGBoost available")
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠ XGBoost not available")

try:
    from lightgbm import LGBMClassifier
    LIGHTGBM_AVAILABLE = True
    print("✓ LightGBM available")
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("⚠ LightGBM not available")

# Settings
warnings.filterwarnings('ignore')
np.random.seed(42)
plt.style.use('seaborn-v0_8')

print("✓ All imports completed successfully!")

# Load datasets
print("Loading datasets...")

# Check if data files exist
required_files = [
    'data/sme_profiles.csv',
    'data/sme_financials.csv', 
    'data/sme_alternative_data.csv',
    'data/sme_loan_applications.csv'
]

missing_files = [f for f in required_files if not os.path.exists(f)]
if missing_files:
    print("❌ Missing required data files:")
    for f in missing_files:
        print(f"  - {f}")
    print("Please run the data synthesis notebook first.")
else:
    print("✓ All data files found")

# Load data
sme_profiles = pd.read_csv('data/sme_profiles.csv')
sme_financials = pd.read_csv('data/sme_financials.csv')
sme_alternative_data = pd.read_csv('data/sme_alternative_data.csv')
sme_loan_applications = pd.read_csv('data/sme_loan_applications.csv')

print(f"✓ SME Profiles: {sme_profiles.shape}")
print(f"✓ SME Financials: {sme_financials.shape}")
print(f"✓ SME Alternative Data: {sme_alternative_data.shape}")
print(f"✓ SME Loan Applications: {sme_loan_applications.shape}")

# Data exploration and quality check
print("Data Quality Overview:")
print("=" * 50)

# Check target distribution
sme_loan_applications['Loan_Default_Status'] = sme_loan_applications['Loan_Default_Status'].astype(int)
default_rate = sme_loan_applications['Loan_Default_Status'].mean()
print(f"Default Rate: {default_rate:.2%}")
print(f"Class Distribution:")
print(sme_loan_applications['Loan_Default_Status'].value_counts())

# Visualize target distribution
fig, axes = plt.subplots(1, 2, figsize=(12, 4))

# Bar plot
sme_loan_applications['Loan_Default_Status'].value_counts().plot(kind='bar', ax=axes[0])
axes[0].set_title('Loan Default Status Distribution')
axes[0].set_xlabel('Default Status (0=No Default, 1=Default)')
axes[0].set_ylabel('Count')

# Pie chart
labels = ['No Default', 'Default']
sizes = sme_loan_applications['Loan_Default_Status'].value_counts().values
axes[1].pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
axes[1].set_title('Default Rate Distribution')

plt.tight_layout()
plt.show()

print(f"\n✓ Data loaded successfully with {default_rate:.1%} default rate")

# Prepare loan applications data
print("Preparing loan applications data...")

# Convert date columns
sme_loan_applications['Loan_Application_Date'] = pd.to_datetime(sme_loan_applications['Loan_Application_Date'])
sme_loan_applications['Observation_EndDate'] = pd.to_datetime(sme_loan_applications['Observation_EndDate'])
sme_loan_applications['Application_Year'] = sme_loan_applications['Loan_Application_Date'].dt.year
sme_loan_applications['Previous_Year'] = sme_loan_applications['Application_Year'] - 1

print(f"✓ Date columns processed")
print(f"Application years: {sorted(sme_loan_applications['Application_Year'].unique())}")

# Merge datasets
print("Merging datasets...")

# Get financial data from the year before loan application
loan_financials = sme_loan_applications.merge(
    sme_financials,
    left_on=['SME_ID', 'Previous_Year'],
    right_on=['SME_ID', 'Year'],
    how='inner'
)

print(f"✓ Merged with financials: {loan_financials.shape}")

# Merge with alternative data
loan_data = loan_financials.merge(sme_alternative_data, on='SME_ID', how='inner')
print(f"✓ Merged with alternative data: {loan_data.shape}")

# Merge with SME profiles
loan_data = loan_data.merge(
    sme_profiles[['SME_ID', 'Industry_Sector', 'Geographic_Location', 
                 'Years_In_Business', 'Geographic_Location_Risk_Tier']], 
    on='SME_ID',
    how='inner'
)

print(f"✓ Final merged dataset: {loan_data.shape}")
print(f"✓ Features available: {loan_data.columns.tolist()[:10]}...")

# Basic Financial Ratios
print("Creating basic financial ratios...")

def create_basic_ratios(df):
    """Create fundamental financial ratios"""
    # Profitability ratios
    df['Profit_Margin'] = df['Net_Income'] / df['Revenue']
    df['Return_on_Assets'] = df['Net_Income'] / df['Total_Assets']
    df['Return_on_Equity'] = df['Net_Income'] / df['Equity']
    df['Operating_Margin'] = (df['Revenue'] - df['Operating_Expense']) / df['Revenue']
    df['Gross_Margin'] = (df['Revenue'] - df['COGS']) / df['Revenue']
    
    # Liquidity ratios
    df['Current_Ratio'] = (df['Cash_Holdings'] + df['Accounts_Receivable']) / df['Accounts_Payable']
    df['Quick_Ratio'] = df['Cash_Holdings'] / df['Accounts_Payable']
    df['Cash_Ratio'] = df['Cash_Holdings'] / df['Total_Liabilities']
    df['Working_Capital'] = df['Cash_Holdings'] + df['Accounts_Receivable'] - df['Accounts_Payable']
    df['Working_Capital_Ratio'] = df['Working_Capital'] / df['Total_Assets']
    
    # Leverage ratios
    df['Debt_to_Equity'] = df['Total_Liabilities'] / df['Equity']
    df['Debt_to_Assets'] = df['Total_Liabilities'] / df['Total_Assets']
    df['Equity_Ratio'] = df['Equity'] / df['Total_Assets']
    df['Financial_Leverage'] = df['Total_Assets'] / df['Equity']
    
    # Efficiency ratios
    df['Asset_Turnover'] = df['Revenue'] / df['Total_Assets']
    df['Inventory_Turnover'] = df['COGS'] / df['Inventory']
    df['Receivables_Turnover'] = df['Revenue'] / df['Accounts_Receivable']
    df['Payables_Turnover'] = df['COGS'] / df['Accounts_Payable']
    
    return df

loan_data = create_basic_ratios(loan_data)
print(f"✓ Basic ratios created. Dataset shape: {loan_data.shape}")

# Advanced Financial Health Indicators
print("Creating advanced financial health indicators...")

def create_advanced_metrics(df):
    """Create sophisticated financial health metrics"""
    
    # DuPont Analysis
    df['DuPont_ROE'] = df['Profit_Margin'] * df['Asset_Turnover'] * df['Financial_Leverage']
    
    # Altman Z-Score components (modified for SMEs)
    df['Working_Capital_to_Assets'] = df['Working_Capital'] / df['Total_Assets']
    df['Retained_Earnings_to_Assets'] = df['Net_Income'] / df['Total_Assets']  # Proxy
    df['EBIT_to_Assets'] = (df['Net_Income'] + df['Operating_Expense'] * 0.1) / df['Total_Assets']
    df['Market_Value_to_Debt'] = df['Equity'] / df['Total_Liabilities']
    df['Sales_to_Assets'] = df['Revenue'] / df['Total_Assets']
    
    # Modified Altman Z-Score for SMEs
    df['Altman_Z_Score'] = (
        1.2 * df['Working_Capital_to_Assets'] +
        1.4 * df['Retained_Earnings_to_Assets'] +
        3.3 * df['EBIT_to_Assets'] +
        0.6 * df['Market_Value_to_Debt'] +
        1.0 * df['Sales_to_Assets']
    )
    
    # Cash conversion cycle
    df['Days_Sales_Outstanding'] = (df['Accounts_Receivable'] / df['Revenue']) * 365
    df['Days_Inventory_Outstanding'] = (df['Inventory'] / df['COGS']) * 365
    df['Days_Payable_Outstanding'] = (df['Accounts_Payable'] / df['COGS']) * 365
    df['Cash_Conversion_Cycle'] = (
        df['Days_Sales_Outstanding'] + 
        df['Days_Inventory_Outstanding'] - 
        df['Days_Payable_Outstanding']
    )
    
    # Interest coverage (estimated)
    df['Interest_Coverage'] = df['Net_Income'] / (df['Total_Liabilities'] * 0.05 + 0.01)
    
    # Operating efficiency
    df['Operating_Efficiency'] = df['Revenue'] / df['Operating_Expense']
    df['Cost_Structure'] = df['COGS'] / df['Revenue']
    df['Expense_Ratio'] = df['Operating_Expense'] / df['Revenue']
    
    return df

loan_data = create_advanced_metrics(loan_data)
print(f"✓ Advanced metrics created. Dataset shape: {loan_data.shape}")

# Risk Indicators and Alternative Data Enhancement
print("Creating risk indicators and enhancing alternative data...")

def create_risk_and_alt_features(df):
    """Create risk indicators and enhance alternative data"""
    
    # Risk indicators
    df['Liquidity_Risk'] = 1 / (df['Current_Ratio'] + 0.01)
    df['Profitability_Risk'] = 1 / (abs(df['Profit_Margin']) + 0.01)
    df['Leverage_Risk'] = df['Debt_to_Equity'] / (df['Debt_to_Equity'].quantile(0.75) + 0.01)
    
    # Business model indicators
    df['Asset_Intensity'] = df['Total_Assets'] / df['Revenue']
    df['Capital_Intensity'] = (df['Total_Assets'] - df['Cash_Holdings']) / df['Revenue']
    
    # Size and performance percentiles
    df['Size_Percentile'] = df['Revenue'].rank(pct=True)
    df['Profitability_Percentile'] = df['Profit_Margin'].rank(pct=True)
    df['Efficiency_Percentile'] = df['Asset_Turnover'].rank(pct=True)
    
    # Alternative data enhancements
    df['Digital_Maturity'] = (df['Online_Presence_Score'] / 10) * np.log1p(df['Years_In_Business'])
    df['Customer_Satisfaction_Index'] = (
        (df['Average_Customer_Review_Score'] / 5) * 
        (1 - df['Number_of_Customer_Complaints_Monthly'] / 20)
    )
    df['Review_to_Complaints_Ratio'] = (
        df['Average_Customer_Review_Score'] / (df['Number_of_Customer_Complaints_Monthly'] + 1)
    )
    
    # Payment behavior
    df['Payment_Reliability'] = 1 - df['Payment_Gateway_Transaction_Volatility']
    df['Digital_Trust_Score'] = (
        0.4 * (df['Online_Presence_Score'] / 10) +
        0.3 * (df['Average_Customer_Review_Score'] / 5) +
        0.2 * (1 - df['Number_of_Customer_Complaints_Monthly'] / 20) +
        0.1 * df['Payment_Reliability']
    )
    
    # Business maturity
    df['Business_Maturity_Score'] = np.log1p(df['Years_In_Business']) * (df['Revenue'] / 1000000)
    
    return df

loan_data = create_risk_and_alt_features(loan_data)
print(f"✓ Risk indicators and alternative data features created. Dataset shape: {loan_data.shape}")

# Interaction Features and Industry Benchmarking
print("Creating interaction features and industry benchmarks...")

def create_interaction_and_industry_features(df):
    """Create interaction features and industry benchmarks"""
    
    # Key interaction features
    df['Profitability_x_Liquidity'] = df['Profit_Margin'] * df['Current_Ratio']
    df['Leverage_x_Profitability'] = df['Debt_to_Equity'] * df['Profit_Margin']
    df['Digital_x_Financial'] = df['Online_Presence_Score'] * df['Asset_Turnover']
    df['Experience_x_Performance'] = df['Years_In_Business'] * df['Return_on_Assets']
    df['Size_x_Efficiency'] = np.log1p(df['Revenue']) * df['Asset_Turnover']
    
    # Industry benchmarking
    for industry in df['Industry_Sector'].unique():
        industry_mask = df['Industry_Sector'] == industry
        
        # Industry medians
        industry_profit_margin = df[industry_mask]['Profit_Margin'].median()
        industry_current_ratio = df[industry_mask]['Current_Ratio'].median()
        industry_debt_equity = df[industry_mask]['Debt_to_Equity'].median()
        
        # Relative performance
        df.loc[industry_mask, 'Profit_Margin_vs_Industry'] = (
            df.loc[industry_mask, 'Profit_Margin'] / (industry_profit_margin + 0.01)
        )
        df.loc[industry_mask, 'Current_Ratio_vs_Industry'] = (
            df.loc[industry_mask, 'Current_Ratio'] / (industry_current_ratio + 0.01)
        )
        df.loc[industry_mask, 'Debt_Equity_vs_Industry'] = (
            df.loc[industry_mask, 'Debt_to_Equity'] / (industry_debt_equity + 0.01)
        )
    
    return df

loan_data = create_interaction_and_industry_features(loan_data)
print(f"✓ Interaction features and industry benchmarks created. Dataset shape: {loan_data.shape}")

# Data Cleaning and Preprocessing
print("Cleaning and preprocessing data...")

def clean_and_preprocess(df):
    """Clean data and handle outliers"""
    
    # Replace infinite values
    df = df.replace([np.inf, -np.inf], np.nan)
    
    # Cap extreme outliers at 99th percentile
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    exclude_cols = ['SME_ID', 'Loan_Application_ID', 'Year', 'Application_Year', 'Previous_Year', 'Loan_Default_Status']
    
    for col in numeric_columns:
        if col not in exclude_cols:
            q99 = df[col].quantile(0.99)
            q01 = df[col].quantile(0.01)
            df[col] = df[col].clip(lower=q01, upper=q99)
    
    # Fill missing values with median
    for col in numeric_columns:
        if col not in exclude_cols and df[col].isnull().any():
            df[col] = df[col].fillna(df[col].median())
    
    return df

loan_data = clean_and_preprocess(loan_data)
print(f"✓ Data cleaned and preprocessed. Dataset shape: {loan_data.shape}")

# Show feature summary
print(f"\nTotal engineered features: {loan_data.shape[1]}")
print(f"Target variable distribution:")
print(loan_data['Loan_Default_Status'].value_counts())
print(f"Default rate: {loan_data['Loan_Default_Status'].mean():.2%}")

# Prepare features for modeling
print("Preparing features for modeling...")

# Define feature columns (exclude ID columns and target)
exclude_columns = [
    'SME_ID', 'Loan_Application_ID', 'Loan_Application_Date', 'Observation_EndDate',
    'Year', 'Application_Year', 'Previous_Year', 'Loan_Default_Status'
]

# Get categorical columns for encoding
categorical_columns = ['Industry_Sector', 'Geographic_Location', 'Geographic_Location_Risk_Tier']

# One-hot encode categorical variables
loan_data_encoded = pd.get_dummies(loan_data, columns=categorical_columns, drop_first=True)

# Get feature columns
feature_columns = [col for col in loan_data_encoded.columns if col not in exclude_columns]

print(f"✓ Total features after encoding: {len(feature_columns)}")
print(f"✓ Categorical features encoded: {categorical_columns}")

# Prepare X and y
X = loan_data_encoded[feature_columns]
y = loan_data_encoded['Loan_Default_Status']

print(f"✓ Feature matrix shape: {X.shape}")
print(f"✓ Target vector shape: {y.shape}")
print(f"✓ Feature columns: {feature_columns[:10]}...")

# Split data
print("Splitting data...")
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print(f"✓ Training set: {X_train.shape}")
print(f"✓ Test set: {X_test.shape}")
print(f"✓ Training target distribution: {np.bincount(y_train)}")
print(f"✓ Test target distribution: {np.bincount(y_test)}")

# Feature selection
print("Performing feature selection...")

# Select top features using statistical tests
k_best = min(50, X_train.shape[1])  # Select top 50 features or all if less
selector = SelectKBest(score_func=f_classif, k=k_best)
X_train_selected = selector.fit_transform(X_train, y_train)
X_test_selected = selector.transform(X_test)

# Get selected feature names
selected_features = [feature_columns[i] for i in selector.get_support(indices=True)]

print(f"✓ Selected {len(selected_features)} features out of {len(feature_columns)}")
print(f"✓ Top 10 selected features: {selected_features[:10]}")

# Scale features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train_selected)
X_test_scaled = scaler.transform(X_test_selected)

print(f"✓ Features scaled using StandardScaler")

# Handle class imbalance with advanced SMOTE
print("Handling class imbalance...")

# Use BorderlineSMOTE for better boundary handling
smote = BorderlineSMOTE(random_state=42, kind='borderline-1')
X_train_balanced, y_train_balanced = smote.fit_resample(X_train_scaled, y_train)

print(f"Original training distribution: {np.bincount(y_train)}")
print(f"Balanced training distribution: {np.bincount(y_train_balanced)}")
print(f"✓ Class imbalance handled with BorderlineSMOTE")

# Setup models
print("Setting up advanced models...")

models = {}

# Logistic Regression with optimized parameters
models['Logistic Regression'] = LogisticRegression(
    random_state=42, max_iter=1000, C=0.1, solver='liblinear'
)

# Random Forest with optimized parameters
models['Random Forest'] = RandomForestClassifier(
    n_estimators=200, max_depth=10, min_samples_split=5,
    min_samples_leaf=2, random_state=42, n_jobs=-1
)

# Extra Trees
models['Extra Trees'] = ExtraTreesClassifier(
    n_estimators=200, max_depth=12, min_samples_split=5,
    min_samples_leaf=2, random_state=42, n_jobs=-1
)

# Gradient Boosting
models['Gradient Boosting'] = GradientBoostingClassifier(
    n_estimators=200, learning_rate=0.1, max_depth=6,
    min_samples_split=5, min_samples_leaf=2, random_state=42
)

# XGBoost (if available)
if XGBOOST_AVAILABLE:
    models['XGBoost'] = XGBClassifier(
        n_estimators=200, learning_rate=0.1, max_depth=6,
        min_child_weight=1, subsample=0.8, colsample_bytree=0.8,
        random_state=42, n_jobs=-1, eval_metric='logloss'
    )
    print("✓ XGBoost model added")

# LightGBM (if available)
if LIGHTGBM_AVAILABLE:
    models['LightGBM'] = LGBMClassifier(
        n_estimators=200, learning_rate=0.1, max_depth=6,
        min_child_samples=20, subsample=0.8, colsample_bytree=0.8,
        random_state=42, n_jobs=-1, verbose=-1
    )
    print("✓ LightGBM model added")

print(f"✓ Total models setup: {len(models)}")
print(f"✓ Models: {list(models.keys())}")

# Train and evaluate individual models
print("Training and evaluating individual models...")
print("=" * 60)

results = {}

for name, model in models.items():
    print(f"\nTraining {name}...")
    
    # Cross-validation
    cv_scores = cross_val_score(
        model, X_train_balanced, y_train_balanced, 
        cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=42),
        scoring='roc_auc', n_jobs=-1
    )
    
    # Train on full training set
    model.fit(X_train_balanced, y_train_balanced)
    
    # Predictions
    y_pred = model.predict(X_test_scaled)
    y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
    
    # Metrics
    accuracy = accuracy_score(y_test, y_pred)
    roc_auc = roc_auc_score(y_test, y_pred_proba)
    
    results[name] = {
        'model': model,
        'accuracy': accuracy,
        'roc_auc': roc_auc,
        'cv_scores': cv_scores,
        'predictions': y_pred,
        'probabilities': y_pred_proba
    }
    
    print(f"  Accuracy: {accuracy:.4f}")
    print(f"  ROC-AUC: {roc_auc:.4f}")
    print(f"  CV ROC-AUC: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

print("\n✓ Individual model training completed")

# Create ensemble models
print("Creating ensemble models...")

# Voting Classifier (use all available models)
voting_models = [(name, model) for name, model in models.items()]
voting_clf = VotingClassifier(estimators=voting_models, voting='soft')
voting_clf.fit(X_train_balanced, y_train_balanced)

y_pred_voting = voting_clf.predict(X_test_scaled)
y_pred_proba_voting = voting_clf.predict_proba(X_test_scaled)[:, 1]

results['Voting Ensemble'] = {
    'model': voting_clf,
    'accuracy': accuracy_score(y_test, y_pred_voting),
    'roc_auc': roc_auc_score(y_test, y_pred_proba_voting),
    'predictions': y_pred_voting,
    'probabilities': y_pred_proba_voting
}

print(f"✓ Voting Ensemble - Accuracy: {results['Voting Ensemble']['accuracy']:.4f}, ROC-AUC: {results['Voting Ensemble']['roc_auc']:.4f}")

# Stacking Classifier (use top 3 models as base)
top_models = sorted(results.items(), key=lambda x: x[1]['roc_auc'], reverse=True)[:3]
base_models = [(name, result['model']) for name, result in top_models if name != 'Voting Ensemble']

if len(base_models) >= 2:
    stacking_clf = StackingClassifier(
        estimators=base_models,
        final_estimator=LogisticRegression(random_state=42),
        cv=3
    )
    stacking_clf.fit(X_train_balanced, y_train_balanced)
    
    y_pred_stacking = stacking_clf.predict(X_test_scaled)
    y_pred_proba_stacking = stacking_clf.predict_proba(X_test_scaled)[:, 1]
    
    results['Stacking Ensemble'] = {
        'model': stacking_clf,
        'accuracy': accuracy_score(y_test, y_pred_stacking),
        'roc_auc': roc_auc_score(y_test, y_pred_proba_stacking),
        'predictions': y_pred_stacking,
        'probabilities': y_pred_proba_stacking
    }
    
    print(f"✓ Stacking Ensemble - Accuracy: {results['Stacking Ensemble']['accuracy']:.4f}, ROC-AUC: {results['Stacking Ensemble']['roc_auc']:.4f}")

print("\n✓ Ensemble models created")

# Display final results
print("ENHANCED SME LOAN DEFAULT PREDICTION RESULTS")
print("=" * 60)

# Create results summary
results_summary = []
for name, result in results.items():
    results_summary.append({
        'Model': name,
        'Accuracy': result['accuracy'],
        'ROC_AUC': result['roc_auc']
    })

results_df = pd.DataFrame(results_summary).sort_values('Accuracy', ascending=False)
print(results_df.to_string(index=False, float_format='%.4f'))

# Find best model
best_model_name = results_df.iloc[0]['Model']
best_accuracy = results_df.iloc[0]['Accuracy']
best_roc_auc = results_df.iloc[0]['ROC_AUC']

print(f"\n🏆 BEST MODEL: {best_model_name}")
print(f"📊 Best Accuracy: {best_accuracy:.4f} ({best_accuracy:.1%})")
print(f"📈 Best ROC-AUC: {best_roc_auc:.4f}")

# Compare with original model
original_accuracy = 0.60
original_roc_auc = 0.686

accuracy_improvement = ((best_accuracy - original_accuracy) / original_accuracy) * 100
roc_improvement = ((best_roc_auc - original_roc_auc) / original_roc_auc) * 100

print(f"\n📈 IMPROVEMENT OVER ORIGINAL MODEL:")
print(f"   Accuracy: {original_accuracy:.3f} → {best_accuracy:.3f} ({accuracy_improvement:+.1f}%)")
print(f"   ROC-AUC:  {original_roc_auc:.3f} → {best_roc_auc:.3f} ({roc_improvement:+.1f}%)")

if best_accuracy > 0.85:
    print("\n🎉 TARGET ACHIEVED: >85% accuracy reached!")
else:
    print(f"\n📊 Progress: {best_accuracy:.1%} accuracy (Target: 85%)")
    remaining = (0.85 - best_accuracy) / best_accuracy * 100
    print(f"   Need {remaining:.1f}% more improvement to reach target")

# Detailed classification report for best model
print(f"\nDETAILED CLASSIFICATION REPORT - {best_model_name}:")
print("=" * 60)
best_predictions = results[best_model_name]['predictions']
print(classification_report(y_test, best_predictions))

# Confusion matrix
cm = confusion_matrix(y_test, best_predictions)
print(f"\nConfusion Matrix:")
print(f"                 Predicted")
print(f"Actual    No Default  Default")
print(f"No Default    {cm[0,0]:6d}    {cm[0,1]:6d}")
print(f"Default       {cm[1,0]:6d}    {cm[1,1]:6d}")

# Visualize model performance comparison
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. Model Accuracy Comparison
results_df_sorted = results_df.sort_values('Accuracy')
axes[0,0].barh(results_df_sorted['Model'], results_df_sorted['Accuracy'])
axes[0,0].set_xlabel('Accuracy')
axes[0,0].set_title('Model Accuracy Comparison')
axes[0,0].axvline(x=0.85, color='red', linestyle='--', label='Target (85%)')
axes[0,0].legend()

# 2. ROC-AUC Comparison
results_df_sorted_auc = results_df.sort_values('ROC_AUC')
axes[0,1].barh(results_df_sorted_auc['Model'], results_df_sorted_auc['ROC_AUC'])
axes[0,1].set_xlabel('ROC-AUC')
axes[0,1].set_title('Model ROC-AUC Comparison')
axes[0,1].axvline(x=0.85, color='red', linestyle='--', label='Target (0.85)')
axes[0,1].legend()

# 3. ROC Curve for best model
best_probabilities = results[best_model_name]['probabilities']
fpr, tpr, _ = roc_curve(y_test, best_probabilities)
axes[1,0].plot(fpr, tpr, label=f'{best_model_name} (AUC = {best_roc_auc:.3f})')
axes[1,0].plot([0, 1], [0, 1], 'k--', label='Random')
axes[1,0].set_xlabel('False Positive Rate')
axes[1,0].set_ylabel('True Positive Rate')
axes[1,0].set_title('ROC Curve - Best Model')
axes[1,0].legend()
axes[1,0].grid(True)

# 4. Confusion Matrix Heatmap
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1,1])
axes[1,1].set_xlabel('Predicted')
axes[1,1].set_ylabel('Actual')
axes[1,1].set_title(f'Confusion Matrix - {best_model_name}')

plt.tight_layout()
plt.show()

print("✓ Performance visualizations created")

# Feature importance analysis (for tree-based models)
best_model = results[best_model_name]['model']

if hasattr(best_model, 'feature_importances_'):
    print(f"\nFEATURE IMPORTANCE ANALYSIS - {best_model_name}:")
    print("=" * 60)
    
    # Get feature importances
    importances = best_model.feature_importances_
    feature_importance_df = pd.DataFrame({
        'Feature': selected_features,
        'Importance': importances
    }).sort_values('Importance', ascending=False)
    
    # Display top 15 features
    print("Top 15 Most Important Features:")
    print(feature_importance_df.head(15).to_string(index=False, float_format='%.4f'))
    
    # Visualize top 10 features
    plt.figure(figsize=(10, 6))
    top_features = feature_importance_df.head(10)
    plt.barh(top_features['Feature'], top_features['Importance'])
    plt.xlabel('Feature Importance')
    plt.title(f'Top 10 Feature Importances - {best_model_name}')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.show()
    
    print("✓ Feature importance analysis completed")
else:
    print(f"\n⚠ Feature importance not available for {best_model_name}")

# Save model artifacts
print("Saving model artifacts...")

# Create directory
os.makedirs('enhanced_models', exist_ok=True)

# Save best model and preprocessing components
joblib.dump(best_model, 'enhanced_models/best_model.joblib')
joblib.dump(scaler, 'enhanced_models/scaler.joblib')
joblib.dump(selector, 'enhanced_models/feature_selector.joblib')

# Save feature information
pd.Series(selected_features).to_csv('enhanced_models/selected_features.csv', index=False, header=['Feature'])
pd.Series(feature_columns).to_csv('enhanced_models/all_features.csv', index=False, header=['Feature'])

# Save model performance summary
results_df.to_csv('enhanced_models/model_performance.csv', index=False)

# Save metadata
metadata = {
    'best_model': best_model_name,
    'best_accuracy': best_accuracy,
    'best_roc_auc': best_roc_auc,
    'original_accuracy': original_accuracy,
    'original_roc_auc': original_roc_auc,
    'accuracy_improvement': accuracy_improvement,
    'roc_improvement': roc_improvement,
    'total_features': len(feature_columns),
    'selected_features': len(selected_features),
    'training_samples': len(X_train),
    'test_samples': len(X_test),
    'target_achieved': best_accuracy > 0.85
}

pd.Series(metadata).to_csv('enhanced_models/model_metadata.csv', header=['Value'])

print("✓ Model artifacts saved to 'enhanced_models/' directory")
print("  - best_model.joblib (trained model)")
print("  - scaler.joblib (feature scaler)")
print("  - feature_selector.joblib (feature selector)")
print("  - selected_features.csv (selected feature names)")
print("  - model_performance.csv (all model results)")
print("  - model_metadata.csv (experiment metadata)")

# Final Summary
print("\n" + "=" * 80)
print("🎯 ENHANCED SME LOAN DEFAULT PREDICTION - FINAL SUMMARY")
print("=" * 80)

print(f"\n📊 PERFORMANCE ACHIEVEMENTS:")
print(f"   • Best Model: {best_model_name}")
print(f"   • Accuracy: {best_accuracy:.1%} (Target: 85%)")
print(f"   • ROC-AUC: {best_roc_auc:.3f} (Target: 0.85)")
print(f"   • Improvement: +{accuracy_improvement:.1f}% accuracy, +{roc_improvement:.1f}% ROC-AUC")

print(f"\n🔧 TECHNICAL IMPROVEMENTS:")
print(f"   • Features: {len(feature_columns)} total, {len(selected_features)} selected")
print(f"   • Models: {len(models)} individual + 2 ensemble models")
print(f"   • Sampling: BorderlineSMOTE for class imbalance")
print(f"   • Validation: Stratified cross-validation")
print(f"   • Selection: Statistical feature selection")

print(f"\n✅ KEY ENHANCEMENTS IMPLEMENTED:")
enhancements = [
    "Advanced feature engineering (40+ features)",
    "Altman Z-Score and financial health indicators",
    "Industry benchmarking and relative metrics",
    "XGBoost and LightGBM models (if available)",
    "Ensemble methods (Voting + Stacking)",
    "BorderlineSMOTE for better class balance",
    "Statistical feature selection",
    "Comprehensive cross-validation"
]

for i, enhancement in enumerate(enhancements, 1):
    print(f"   {i}. {enhancement}")

if best_accuracy > 0.85:
    print(f"\n🎉 SUCCESS: Target accuracy of 85% achieved!")
    print(f"   The enhanced model shows significant improvement over the original 60% accuracy.")
else:
    print(f"\n📈 PROGRESS: Substantial improvement achieved")
    print(f"   Consider further hyperparameter tuning or additional features for 85% target.")

print(f"\n💾 All model artifacts saved in 'enhanced_models/' directory")
print(f"🔄 Ready for production deployment or further optimization")
print("=" * 80)