{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Enhanced SME Loan Default Prediction Model\n", "\n", "This notebook implements advanced machine learning techniques to achieve **>85% accuracy** for SME loan default prediction.\n", "\n", "## Key Improvements:\n", "- **Advanced Models**: XGB<PERSON>t, LightGBM, <PERSON><PERSON><PERSON>, Ensemble Methods\n", "- **Enhanced Features**: 40+ engineered features including Altman Z-Score\n", "- **Better Preprocessing**: BorderlineSMOTE, feature selection, outlier handling\n", "- **Robust Validation**: Stratified cross-validation\n", "\n", "## Expected Performance:\n", "- **Current**: 60% accuracy, 0.686 ROC-AUC\n", "- **Target**: 85%+ accuracy, 0.85+ ROC-AUC"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# Install required packages (run this cell first if packages are missing)\n", "import subprocess\n", "import sys\n", "\n", "def install_package(package):\n", "    try:\n", "        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])\n", "        print(f\"✓ {package} installed successfully\")\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"⚠ Warning: Could not install {package}. Error: {e}\")\n", "\n", "# Uncomment and run if you need to install packages\n", "# install_package('xgboost>=1.5.0')\n", "# install_package('lightgbm>=3.3.0')\n", "# install_package('optuna>=2.10.0')\n", "# install_package('plotly>=5.0.0')"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# Core imports\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "import os\n", "from datetime import datetime\n", "import joblib\n", "\n", "# Visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# Scikit-learn imports\n", "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score, GridSearchCV\n", "from sklearn.preprocessing import StandardScaler, PolynomialFeatures\n", "from sklearn.feature_selection import SelectKBest, f_classif, RFE\n", "from sklearn.metrics import (\n", "    accuracy_score, classification_report, roc_auc_score, \n", "    confusion_matrix, roc_curve, precision_recall_curve\n", ")\n", "\n", "# Models\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import (\n", "    RandomForestClassifier, GradientBoostingClassifier, \n", "    ExtraTreesClassifier, VotingClassifier, StackingClassifier\n", ")\n", "from sklearn.svm import SVC\n", "\n", "# Imbalanced learning\n", "from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE\n", "from imblearn.combine import SMOTEENN\n", "\n", "# Advanced models (optional)\n", "try:\n", "    from xgboost import XGBClassifier\n", "    XGBOOST_AVAILABLE = True\n", "    print(\"✓ XGBoost available\")\n", "except ImportError:\n", "    XGBOOST_AVAILABLE = False\n", "    print(\"⚠ XGBoost not available\")\n", "\n", "try:\n", "    from lightgbm import LGBMClassifier\n", "    LIGHTGBM_AVAILABLE = True\n", "    print(\"✓ LightGBM available\")\n", "except ImportError:\n", "    LIGHTGBM_AVAILABLE = False\n", "    print(\"⚠ LightGBM not available\")\n", "\n", "# Settings\n", "warnings.filterwarnings('ignore')\n", "np.random.seed(42)\n", "plt.style.use('seaborn-v0_8')\n", "\n", "print(\"✓ All imports completed successfully!\")"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## 2. Data Loading and Initial Exploration"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# Load datasets\n", "print(\"Loading datasets...\")\n", "\n", "# Check if data files exist\n", "required_files = [\n", "    'data/sme_profiles.csv',\n", "    'data/sme_financials.csv', \n", "    'data/sme_alternative_data.csv',\n", "    'data/sme_loan_applications.csv'\n", "]\n", "\n", "missing_files = [f for f in required_files if not os.path.exists(f)]\n", "if missing_files:\n", "    print(\"❌ Missing required data files:\")\n", "    for f in missing_files:\n", "        print(f\"  - {f}\")\n", "    print(\"Please run the data synthesis notebook first.\")\n", "else:\n", "    print(\"✓ All data files found\")\n", "\n", "# Load data\n", "sme_profiles = pd.read_csv('data/sme_profiles.csv')\n", "sme_financials = pd.read_csv('data/sme_financials.csv')\n", "sme_alternative_data = pd.read_csv('data/sme_alternative_data.csv')\n", "sme_loan_applications = pd.read_csv('data/sme_loan_applications.csv')\n", "\n", "print(f\"✓ SME Profiles: {sme_profiles.shape}\")\n", "print(f\"✓ SME Financials: {sme_financials.shape}\")\n", "print(f\"✓ SME Alternative Data: {sme_alternative_data.shape}\")\n", "print(f\"✓ SME Loan Applications: {sme_loan_applications.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["# Data exploration and quality check\n", "print(\"Data Quality Overview:\")\n", "print(\"=\" * 50)\n", "\n", "# Check target distribution\n", "sme_loan_applications['Loan_Default_Status'] = sme_loan_applications['Loan_Default_Status'].astype(int)\n", "default_rate = sme_loan_applications['Loan_Default_Status'].mean()\n", "print(f\"Default Rate: {default_rate:.2%}\")\n", "print(f\"Class Distribution:\")\n", "print(sme_loan_applications['Loan_Default_Status'].value_counts())\n", "\n", "# Visualize target distribution\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 4))\n", "\n", "# Bar plot\n", "sme_loan_applications['Loan_Default_Status'].value_counts().plot(kind='bar', ax=axes[0])\n", "axes[0].set_title('Loan Default Status Distribution')\n", "axes[0].set_xlabel('Default Status (0=No Default, 1=Default)')\n", "axes[0].set_ylabel('Count')\n", "\n", "# Pie chart\n", "labels = ['No Default', 'Default']\n", "sizes = sme_loan_applications['Loan_Default_Status'].value_counts().values\n", "axes[1].pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)\n", "axes[1].set_title('Default Rate Distribution')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n✓ Data loaded successfully with {default_rate:.1%} default rate\")"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## 3. Data Preparation and Merging"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# Prepare loan applications data\n", "print(\"Preparing loan applications data...\")\n", "\n", "# Convert date columns\n", "sme_loan_applications['Loan_Application_Date'] = pd.to_datetime(sme_loan_applications['Loan_Application_Date'])\n", "sme_loan_applications['Observation_EndDate'] = pd.to_datetime(sme_loan_applications['Observation_EndDate'])\n", "sme_loan_applications['Application_Year'] = sme_loan_applications['Loan_Application_Date'].dt.year\n", "sme_loan_applications['Previous_Year'] = sme_loan_applications['Application_Year'] - 1\n", "\n", "print(f\"✓ Date columns processed\")\n", "print(f\"Application years: {sorted(sme_loan_applications['Application_Year'].unique())}\")"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["# Merge datasets\n", "print(\"Merging datasets...\")\n", "\n", "# Get financial data from the year before loan application\n", "loan_financials = sme_loan_applications.merge(\n", "    sme_financials,\n", "    left_on=['SME_ID', 'Previous_Year'],\n", "    right_on=['SME_ID', 'Year'],\n", "    how='inner'\n", ")\n", "\n", "print(f\"✓ Merged with financials: {loan_financials.shape}\")\n", "\n", "# Merge with alternative data\n", "loan_data = loan_financials.merge(sme_alternative_data, on='SME_ID', how='inner')\n", "print(f\"✓ Merged with alternative data: {loan_data.shape}\")\n", "\n", "# Merge with SME profiles\n", "loan_data = loan_data.merge(\n", "    sme_profiles[['SME_ID', 'Industry_Sector', 'Geographic_Location', \n", "                 'Years_In_Business', 'Geographic_Location_Risk_Tier']], \n", "    on='SME_ID',\n", "    how='inner'\n", ")\n", "\n", "print(f\"✓ Final merged dataset: {loan_data.shape}\")\n", "print(f\"✓ Features available: {loan_data.columns.tolist()[:10]}...\")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["## 4. Advanced Feature Engineering"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# Basic Financial Ratios\n", "print(\"Creating basic financial ratios...\")\n", "\n", "def create_basic_ratios(df):\n", "    \"\"\"Create fundamental financial ratios\"\"\"\n", "    # Profitability ratios\n", "    df['Profit_Margin'] = df['Net_Income'] / df['Revenue']\n", "    df['Return_on_Assets'] = df['Net_Income'] / df['Total_Assets']\n", "    df['Return_on_Equity'] = df['Net_Income'] / df['Equity']\n", "    df['Operating_Margin'] = (df['Revenue'] - df['Operating_Expense']) / df['Revenue']\n", "    df['Gross_Margin'] = (df['Revenue'] - df['COGS']) / df['Revenue']\n", "    \n", "    # Liquidity ratios\n", "    df['Current_Ratio'] = (df['Cash_Holdings'] + df['Accounts_Receivable']) / df['Accounts_Payable']\n", "    df['Quick_Ratio'] = df['Cash_Holdings'] / df['Accounts_Payable']\n", "    df['Cash_Ratio'] = df['Cash_Holdings'] / df['Total_Liabilities']\n", "    df['Working_Capital'] = df['Cash_Holdings'] + df['Accounts_Receivable'] - df['Accounts_Payable']\n", "    df['Working_Capital_Ratio'] = df['Working_Capital'] / df['Total_Assets']\n", "    \n", "    # Leverage ratios\n", "    df['Debt_to_Equity'] = df['Total_Liabilities'] / df['Equity']\n", "    df['Debt_to_Assets'] = df['Total_Liabilities'] / df['Total_Assets']\n", "    df['Equity_Ratio'] = df['Equity'] / df['Total_Assets']\n", "    df['Financial_Leverage'] = df['Total_Assets'] / df['Equity']\n", "    \n", "    # Efficiency ratios\n", "    df['Asset_Turnover'] = df['Revenue'] / df['Total_Assets']\n", "    df['Inventory_Turnover'] = df['COGS'] / df['Inventory']\n", "    df['Receivables_Turnover'] = df['Revenue'] / df['Accounts_Receivable']\n", "    df['Payables_Turnover'] = df['COGS'] / df['Accounts_Payable']\n", "    \n", "    return df\n", "\n", "loan_data = create_basic_ratios(loan_data)\n", "print(f\"✓ Basic ratios created. Dataset shape: {loan_data.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["# Advanced Financial Health Indicators\n", "print(\"Creating advanced financial health indicators...\")\n", "\n", "def create_advanced_metrics(df):\n", "    \"\"\"Create sophisticated financial health metrics\"\"\"\n", "    \n", "    # DuPont Analysis\n", "    df['DuPont_ROE'] = df['Profit_Margin'] * df['Asset_Turnover'] * df['Financial_Leverage']\n", "    \n", "    # Altman Z-Score components (modified for SMEs)\n", "    df['Working_Capital_to_Assets'] = df['Working_Capital'] / df['Total_Assets']\n", "    df['Retained_Earnings_to_Assets'] = df['Net_Income'] / df['Total_Assets']  # Proxy\n", "    df['EBIT_to_Assets'] = (df['Net_Income'] + df['Operating_Expense'] * 0.1) / df['Total_Assets']\n", "    df['Market_Value_to_Debt'] = df['Equity'] / df['Total_Liabilities']\n", "    df['Sales_to_Assets'] = df['Revenue'] / df['Total_Assets']\n", "    \n", "    # Modified Altman Z-Score for SMEs\n", "    df['<PERSON>man_Z_Score'] = (\n", "        1.2 * df['Working_Capital_to_Assets'] +\n", "        1.4 * df['Retained_Earnings_to_Assets'] +\n", "        3.3 * df['EBIT_to_Assets'] +\n", "        0.6 * df['Market_Value_to_Debt'] +\n", "        1.0 * df['Sales_to_Assets']\n", "    )\n", "    \n", "    # Cash conversion cycle\n", "    df['Days_Sales_Outstanding'] = (df['Accounts_Receivable'] / df['Revenue']) * 365\n", "    df['Days_Inventory_Outstanding'] = (df['Inventory'] / df['COGS']) * 365\n", "    df['Days_Payable_Outstanding'] = (df['Accounts_Payable'] / df['COGS']) * 365\n", "    df['Cash_Conversion_Cycle'] = (\n", "        df['Days_Sales_Outstanding'] + \n", "        df['Days_Inventory_Outstanding'] - \n", "        df['Days_Payable_Outstanding']\n", "    )\n", "    \n", "    # Interest coverage (estimated)\n", "    df['Interest_Coverage'] = df['Net_Income'] / (df['Total_Liabilities'] * 0.05 + 0.01)\n", "    \n", "    # Operating efficiency\n", "    df['Operating_Efficiency'] = df['Revenue'] / df['Operating_Expense']\n", "    df['Cost_Structure'] = df['COGS'] / df['Revenue']\n", "    df['Expense_Ratio'] = df['Operating_Expense'] / df['Revenue']\n", "    \n", "    return df\n", "\n", "loan_data = create_advanced_metrics(loan_data)\n", "print(f\"✓ Advanced metrics created. Dataset shape: {loan_data.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# Risk Indicators and Alternative Data Enhancement\n", "print(\"Creating risk indicators and enhancing alternative data...\")\n", "\n", "def create_risk_and_alt_features(df):\n", "    \"\"\"Create risk indicators and enhance alternative data\"\"\"\n", "    \n", "    # Risk indicators\n", "    df['Liquidity_Risk'] = 1 / (df['Current_Ratio'] + 0.01)\n", "    df['Profitability_Risk'] = 1 / (abs(df['Profit_Margin']) + 0.01)\n", "    df['Leverage_Risk'] = df['Debt_to_Equity'] / (df['Debt_to_Equity'].quantile(0.75) + 0.01)\n", "    \n", "    # Business model indicators\n", "    df['Asset_Intensity'] = df['Total_Assets'] / df['Revenue']\n", "    df['Capital_Intensity'] = (df['Total_Assets'] - df['Cash_Holdings']) / df['Revenue']\n", "    \n", "    # Size and performance percentiles\n", "    df['Size_Percentile'] = df['Revenue'].rank(pct=True)\n", "    df['Profitability_Percentile'] = df['Profit_Margin'].rank(pct=True)\n", "    df['Efficiency_Percentile'] = df['Asset_Turnover'].rank(pct=True)\n", "    \n", "    # Alternative data enhancements\n", "    df['Digital_Maturity'] = (df['Online_Presence_Score'] / 10) * np.log1p(df['Years_In_Business'])\n", "    df['Customer_Satisfaction_Index'] = (\n", "        (df['Average_Customer_Review_Score'] / 5) * \n", "        (1 - df['Number_of_Customer_Complaints_Monthly'] / 20)\n", "    )\n", "    df['Review_to_Complaints_Ratio'] = (\n", "        df['Average_Customer_Review_Score'] / (df['Number_of_Customer_Complaints_Monthly'] + 1)\n", "    )\n", "    \n", "    # Payment behavior\n", "    df['Payment_Reliability'] = 1 - df['Payment_Gateway_Transaction_Volatility']\n", "    df['Digital_Trust_Score'] = (\n", "        0.4 * (df['Online_Presence_Score'] / 10) +\n", "        0.3 * (df['Average_Customer_Review_Score'] / 5) +\n", "        0.2 * (1 - df['Number_of_Customer_Complaints_Monthly'] / 20) +\n", "        0.1 * df['Payment_Reliability']\n", "    )\n", "    \n", "    # Business maturity\n", "    df['Business_Maturity_Score'] = np.log1p(df['Years_In_Business']) * (df['Revenue'] / 1000000)\n", "    \n", "    return df\n", "\n", "loan_data = create_risk_and_alt_features(loan_data)\n", "print(f\"✓ Risk indicators and alternative data features created. Dataset shape: {loan_data.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# Interaction Features and Industry Benchmarking\n", "print(\"Creating interaction features and industry benchmarks...\")\n", "\n", "def create_interaction_and_industry_features(df):\n", "    \"\"\"Create interaction features and industry benchmarks\"\"\"\n", "    \n", "    # Key interaction features\n", "    df['Profitability_x_Liquidity'] = df['Profit_Margin'] * df['Current_Ratio']\n", "    df['Leverage_x_Profitability'] = df['Debt_to_Equity'] * df['Profit_Margin']\n", "    df['Digital_x_Financial'] = df['Online_Presence_Score'] * df['Asset_Turnover']\n", "    df['Experience_x_Performance'] = df['Years_In_Business'] * df['Return_on_Assets']\n", "    df['Size_x_Efficiency'] = np.log1p(df['Revenue']) * df['Asset_Turnover']\n", "    \n", "    # Industry benchmarking\n", "    for industry in df['Industry_Sector'].unique():\n", "        industry_mask = df['Industry_Sector'] == industry\n", "        \n", "        # Industry medians\n", "        industry_profit_margin = df[industry_mask]['Profit_Margin'].median()\n", "        industry_current_ratio = df[industry_mask]['Current_Ratio'].median()\n", "        industry_debt_equity = df[industry_mask]['Debt_to_Equity'].median()\n", "        \n", "        # Relative performance\n", "        df.loc[industry_mask, 'Profit_Margin_vs_Industry'] = (\n", "            df.loc[industry_mask, 'Profit_Margin'] / (industry_profit_margin + 0.01)\n", "        )\n", "        df.loc[industry_mask, 'Current_Ratio_vs_Industry'] = (\n", "            df.loc[industry_mask, 'Current_Ratio'] / (industry_current_ratio + 0.01)\n", "        )\n", "        df.loc[industry_mask, 'Debt_Equity_vs_Industry'] = (\n", "            df.loc[industry_mask, 'Debt_to_Equity'] / (industry_debt_equity + 0.01)\n", "        )\n", "    \n", "    return df\n", "\n", "loan_data = create_interaction_and_industry_features(loan_data)\n", "print(f\"✓ Interaction features and industry benchmarks created. Dataset shape: {loan_data.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["# Data Cleaning and Preprocessing\n", "print(\"Cleaning and preprocessing data...\")\n", "\n", "def clean_and_preprocess(df):\n", "    \"\"\"Clean data and handle outliers\"\"\"\n", "    \n", "    # Replace infinite values\n", "    df = df.replace([np.inf, -np.inf], np.nan)\n", "    \n", "    # Cap extreme outliers at 99th percentile\n", "    numeric_columns = df.select_dtypes(include=[np.number]).columns\n", "    exclude_cols = ['SME_ID', 'Loan_Application_ID', 'Year', 'Application_Year', 'Previous_Year', 'Loan_Default_Status']\n", "    \n", "    for col in numeric_columns:\n", "        if col not in exclude_cols:\n", "            q99 = df[col].quantile(0.99)\n", "            q01 = df[col].quantile(0.01)\n", "            df[col] = df[col].clip(lower=q01, upper=q99)\n", "    \n", "    # Fill missing values with median\n", "    for col in numeric_columns:\n", "        if col not in exclude_cols and df[col].isnull().any():\n", "            df[col] = df[col].fillna(df[col].median())\n", "    \n", "    return df\n", "\n", "loan_data = clean_and_preprocess(loan_data)\n", "print(f\"✓ Data cleaned and preprocessed. Dataset shape: {loan_data.shape}\")\n", "\n", "# Show feature summary\n", "print(f\"\\nTotal engineered features: {loan_data.shape[1]}\")\n", "print(f\"Target variable distribution:\")\n", "print(loan_data['Loan_Default_Status'].value_counts())\n", "print(f\"Default rate: {loan_data['Loan_Default_Status'].mean():.2%}\")"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## 5. Feature Selection and Data Preparation"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["# Prepare features for modeling\n", "print(\"Preparing features for modeling...\")\n", "\n", "# Define feature columns (exclude ID columns and target)\n", "exclude_columns = [\n", "    'SME_ID', 'Loan_Application_ID', 'Loan_Application_Date', 'Observation_EndDate',\n", "    'Year', 'Application_Year', 'Previous_Year', '<PERSON><PERSON>_De<PERSON>ult_Status'\n", "]\n", "\n", "# Get categorical columns for encoding\n", "categorical_columns = ['Industry_Sector', 'Geographic_Location', 'Geographic_Location_Risk_Tier']\n", "\n", "# One-hot encode categorical variables\n", "loan_data_encoded = pd.get_dummies(loan_data, columns=categorical_columns, drop_first=True)\n", "\n", "# Get feature columns\n", "feature_columns = [col for col in loan_data_encoded.columns if col not in exclude_columns]\n", "\n", "print(f\"✓ Total features after encoding: {len(feature_columns)}\")\n", "print(f\"✓ Categorical features encoded: {categorical_columns}\")\n", "\n", "# Prepare X and y\n", "X = loan_data_encoded[feature_columns]\n", "y = loan_data_encoded['Loan_Default_Status']\n", "\n", "print(f\"✓ Feature matrix shape: {X.shape}\")\n", "print(f\"✓ Target vector shape: {y.shape}\")\n", "print(f\"✓ Feature columns: {feature_columns[:10]}...\")"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["## 6. Model Setup and Training"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["# Split data\n", "print(\"Splitting data...\")\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "print(f\"✓ Training set: {X_train.shape}\")\n", "print(f\"✓ Test set: {X_test.shape}\")\n", "print(f\"✓ Training target distribution: {np.bincount(y_train)}\")\n", "print(f\"✓ Test target distribution: {np.bincount(y_test)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["# Feature selection\n", "print(\"Performing feature selection...\")\n", "\n", "# Select top features using statistical tests\n", "k_best = min(50, X_train.shape[1])  # Select top 50 features or all if less\n", "selector = SelectKBest(score_func=f_classif, k=k_best)\n", "X_train_selected = selector.fit_transform(X_train, y_train)\n", "X_test_selected = selector.transform(X_test)\n", "\n", "# Get selected feature names\n", "selected_features = [feature_columns[i] for i in selector.get_support(indices=True)]\n", "\n", "print(f\"✓ Selected {len(selected_features)} features out of {len(feature_columns)}\")\n", "print(f\"✓ Top 10 selected features: {selected_features[:10]}\")\n", "\n", "# Scale features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train_selected)\n", "X_test_scaled = scaler.transform(X_test_selected)\n", "\n", "print(f\"✓ Features scaled using StandardScaler\")"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["# Handle class imbalance with advanced SMOTE\n", "print(\"Handling class imbalance...\")\n", "\n", "# Use BorderlineSMOTE for better boundary handling\n", "smote = BorderlineSMOTE(random_state=42, kind='borderline-1')\n", "X_train_balanced, y_train_balanced = smote.fit_resample(X_train_scaled, y_train)\n", "\n", "print(f\"Original training distribution: {np.bincount(y_train)}\")\n", "print(f\"Balanced training distribution: {np.bincount(y_train_balanced)}\")\n", "print(f\"✓ Class imbalance handled with BorderlineSMOTE\")"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["# Setup models\n", "print(\"Setting up advanced models...\")\n", "\n", "models = {}\n", "\n", "# Logistic Regression with optimized parameters\n", "models['Logistic Regression'] = LogisticRegression(\n", "    random_state=42, max_iter=1000, C=0.1, solver='liblinear'\n", ")\n", "\n", "# Random Forest with optimized parameters\n", "models['Random Forest'] = RandomForestClassifier(\n", "    n_estimators=200, max_depth=10, min_samples_split=5,\n", "    min_samples_leaf=2, random_state=42, n_jobs=-1\n", ")\n", "\n", "# Extra Trees\n", "models['Extra Trees'] = ExtraTreesClassifier(\n", "    n_estimators=200, max_depth=12, min_samples_split=5,\n", "    min_samples_leaf=2, random_state=42, n_jobs=-1\n", ")\n", "\n", "# Gradient Boosting\n", "models['Gradient Boosting'] = GradientBoostingClassifier(\n", "    n_estimators=200, learning_rate=0.1, max_depth=6,\n", "    min_samples_split=5, min_samples_leaf=2, random_state=42\n", ")\n", "\n", "# XGBoost (if available)\n", "if XGBOOST_AVAILABLE:\n", "    models['XGBoost'] = XGBClassifier(\n", "        n_estimators=200, learning_rate=0.1, max_depth=6,\n", "        min_child_weight=1, subsample=0.8, colsample_bytree=0.8,\n", "        random_state=42, n_jobs=-1, eval_metric='logloss'\n", "    )\n", "    print(\"✓ XGBoost model added\")\n", "\n", "# LightGBM (if available)\n", "if LIGHTGBM_AVAILABLE:\n", "    models['LightGBM'] = LGBMClassifier(\n", "        n_estimators=200, learning_rate=0.1, max_depth=6,\n", "        min_child_samples=20, subsample=0.8, colsample_bytree=0.8,\n", "        random_state=42, n_jobs=-1, verbose=-1\n", "    )\n", "    print(\"✓ LightGBM model added\")\n", "\n", "print(f\"✓ Total models setup: {len(models)}\")\n", "print(f\"✓ Models: {list(models.keys())}\")"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["## 7. Model Training and Evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["# Train and evaluate individual models\n", "print(\"Training and evaluating individual models...\")\n", "print(\"=\" * 60)\n", "\n", "results = {}\n", "\n", "for name, model in models.items():\n", "    print(f\"\\nTraining {name}...\")\n", "    \n", "    # Cross-validation\n", "    cv_scores = cross_val_score(\n", "        model, X_train_balanced, y_train_balanced, \n", "        cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=42),\n", "        scoring='roc_auc', n_jobs=-1\n", "    )\n", "    \n", "    # Train on full training set\n", "    model.fit(X_train_balanced, y_train_balanced)\n", "    \n", "    # Predictions\n", "    y_pred = model.predict(X_test_scaled)\n", "    y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]\n", "    \n", "    # Metrics\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    roc_auc = roc_auc_score(y_test, y_pred_proba)\n", "    \n", "    results[name] = {\n", "        'model': model,\n", "        'accuracy': accuracy,\n", "        'roc_auc': roc_auc,\n", "        'cv_scores': cv_scores,\n", "        'predictions': y_pred,\n", "        'probabilities': y_pred_proba\n", "    }\n", "    \n", "    print(f\"  Accuracy: {accuracy:.4f}\")\n", "    print(f\"  ROC-AUC: {roc_auc:.4f}\")\n", "    print(f\"  CV ROC-AUC: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})\")\n", "\n", "print(\"\\n✓ Individual model training completed\")"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["# Create ensemble models\n", "print(\"Creating ensemble models...\")\n", "\n", "# Voting Classifier (use all available models)\n", "voting_models = [(name, model) for name, model in models.items()]\n", "voting_clf = VotingClassifier(estimators=voting_models, voting='soft')\n", "voting_clf.fit(X_train_balanced, y_train_balanced)\n", "\n", "y_pred_voting = voting_clf.predict(X_test_scaled)\n", "y_pred_proba_voting = voting_clf.predict_proba(X_test_scaled)[:, 1]\n", "\n", "results['Voting Ensemble'] = {\n", "    'model': voting_clf,\n", "    'accuracy': accuracy_score(y_test, y_pred_voting),\n", "    'roc_auc': roc_auc_score(y_test, y_pred_proba_voting),\n", "    'predictions': y_pred_voting,\n", "    'probabilities': y_pred_proba_voting\n", "}\n", "\n", "print(f\"✓ Voting Ensemble - Accuracy: {results['Voting Ensemble']['accuracy']:.4f}, ROC-AUC: {results['Voting Ensemble']['roc_auc']:.4f}\")\n", "\n", "# Stacking Classifier (use top 3 models as base)\n", "top_models = sorted(results.items(), key=lambda x: x[1]['roc_auc'], reverse=True)[:3]\n", "base_models = [(name, result['model']) for name, result in top_models if name != 'Voting Ensemble']\n", "\n", "if len(base_models) >= 2:\n", "    stacking_clf = StackingClassifier(\n", "        estimators=base_models,\n", "        final_estimator=LogisticRegression(random_state=42),\n", "        cv=3\n", "    )\n", "    stacking_clf.fit(X_train_balanced, y_train_balanced)\n", "    \n", "    y_pred_stacking = stacking_clf.predict(X_test_scaled)\n", "    y_pred_proba_stacking = stacking_clf.predict_proba(X_test_scaled)[:, 1]\n", "    \n", "    results['Stacking Ensemble'] = {\n", "        'model': stacking_clf,\n", "        'accuracy': accuracy_score(y_test, y_pred_stacking),\n", "        'roc_auc': roc_auc_score(y_test, y_pred_proba_stacking),\n", "        'predictions': y_pred_stacking,\n", "        'probabilities': y_pred_proba_stacking\n", "    }\n", "    \n", "    print(f\"✓ Stacking Ensemble - Accuracy: {results['Stacking Ensemble']['accuracy']:.4f}, ROC-AUC: {results['Stacking Ensemble']['roc_auc']:.4f}\")\n", "\n", "print(\"\\n✓ Ensemble models created\")"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["## 8. Results Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["# Display final results\n", "print(\"ENHANCED SME LOAN DEFAULT PREDICTION RESULTS\")\n", "print(\"=\" * 60)\n", "\n", "# Create results summary\n", "results_summary = []\n", "for name, result in results.items():\n", "    results_summary.append({\n", "        'Model': name,\n", "        'Accuracy': result['accuracy'],\n", "        'ROC_AUC': result['roc_auc']\n", "    })\n", "\n", "results_df = pd.DataFrame(results_summary).sort_values('Accuracy', ascending=False)\n", "print(results_df.to_string(index=False, float_format='%.4f'))\n", "\n", "# Find best model\n", "best_model_name = results_df.iloc[0]['Model']\n", "best_accuracy = results_df.iloc[0]['Accuracy']\n", "best_roc_auc = results_df.iloc[0]['ROC_AUC']\n", "\n", "print(f\"\\n🏆 BEST MODEL: {best_model_name}\")\n", "print(f\"📊 Best Accuracy: {best_accuracy:.4f} ({best_accuracy:.1%})\")\n", "print(f\"📈 Best ROC-AUC: {best_roc_auc:.4f}\")\n", "\n", "# Compare with original model\n", "original_accuracy = 0.60\n", "original_roc_auc = 0.686\n", "\n", "accuracy_improvement = ((best_accuracy - original_accuracy) / original_accuracy) * 100\n", "roc_improvement = ((best_roc_auc - original_roc_auc) / original_roc_auc) * 100\n", "\n", "print(f\"\\n📈 IMPROVEMENT OVER ORIGINAL MODEL:\")\n", "print(f\"   Accuracy: {original_accuracy:.3f} → {best_accuracy:.3f} ({accuracy_improvement:+.1f}%)\")\n", "print(f\"   ROC-AUC:  {original_roc_auc:.3f} → {best_roc_auc:.3f} ({roc_improvement:+.1f}%)\")\n", "\n", "if best_accuracy > 0.85:\n", "    print(\"\\n🎉 TARGET ACHIEVED: >85% accuracy reached!\")\n", "else:\n", "    print(f\"\\n📊 Progress: {best_accuracy:.1%} accuracy (Target: 85%)\")\n", "    remaining = (0.85 - best_accuracy) / best_accuracy * 100\n", "    print(f\"   Need {remaining:.1f}% more improvement to reach target\")"]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["# Detailed classification report for best model\n", "print(f\"\\nDETAILED CLASSIFICATION REPORT - {best_model_name}:\")\n", "print(\"=\" * 60)\n", "best_predictions = results[best_model_name]['predictions']\n", "print(classification_report(y_test, best_predictions))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(y_test, best_predictions)\n", "print(f\"\\nConfusion Matrix:\")\n", "print(f\"                 Predicted\")\n", "print(f\"Actual    No Default  Default\")\n", "print(f\"No Default    {cm[0,0]:6d}    {cm[0,1]:6d}\")\n", "print(f\"Default       {cm[1,0]:6d}    {cm[1,1]:6d}\")"]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["# Visualize model performance comparison\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. Model Accuracy Comparison\n", "results_df_sorted = results_df.sort_values('Accuracy')\n", "axes[0,0].barh(results_df_sorted['Model'], results_df_sorted['Accuracy'])\n", "axes[0,0].set_xlabel('Accuracy')\n", "axes[0,0].set_title('Model Accuracy Comparison')\n", "axes[0,0].axvline(x=0.85, color='red', linestyle='--', label='Target (85%)')\n", "axes[0,0].legend()\n", "\n", "# 2. ROC-AUC Comparison\n", "results_df_sorted_auc = results_df.sort_values('ROC_AUC')\n", "axes[0,1].barh(results_df_sorted_auc['Model'], results_df_sorted_auc['ROC_AUC'])\n", "axes[0,1].set_xlabel('ROC-AUC')\n", "axes[0,1].set_title('Model ROC-AUC Comparison')\n", "axes[0,1].axvline(x=0.85, color='red', linestyle='--', label='Target (0.85)')\n", "axes[0,1].legend()\n", "\n", "# 3. ROC Curve for best model\n", "best_probabilities = results[best_model_name]['probabilities']\n", "fpr, tpr, _ = roc_curve(y_test, best_probabilities)\n", "axes[1,0].plot(fpr, tpr, label=f'{best_model_name} (AUC = {best_roc_auc:.3f})')\n", "axes[1,0].plot([0, 1], [0, 1], 'k--', label='Random')\n", "axes[1,0].set_xlabel('False Positive Rate')\n", "axes[1,0].set_ylabel('True Positive Rate')\n", "axes[1,0].set_title('ROC Curve - Best Model')\n", "axes[1,0].legend()\n", "axes[1,0].grid(True)\n", "\n", "# 4. Confusion Matrix Heatmap\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[1,1])\n", "axes[1,1].set_xlabel('Predicted')\n", "axes[1,1].set_ylabel('Actual')\n", "axes[1,1].set_title(f'Confusion Matrix - {best_model_name}')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ Performance visualizations created\")"]}, {"cell_type": "code", "execution_count": null, "id": "31", "metadata": {}, "outputs": [], "source": ["# Feature importance analysis (for tree-based models)\n", "best_model = results[best_model_name]['model']\n", "\n", "if hasattr(best_model, 'feature_importances_'):\n", "    print(f\"\\nFEATURE IMPORTANCE ANALYSIS - {best_model_name}:\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Get feature importances\n", "    importances = best_model.feature_importances_\n", "    feature_importance_df = pd.DataFrame({\n", "        'Feature': selected_features,\n", "        'Importance': importances\n", "    }).sort_values('Importance', ascending=False)\n", "    \n", "    # Display top 15 features\n", "    print(\"Top 15 Most Important Features:\")\n", "    print(feature_importance_df.head(15).to_string(index=False, float_format='%.4f'))\n", "    \n", "    # Visualize top 10 features\n", "    plt.figure(figsize=(10, 6))\n", "    top_features = feature_importance_df.head(10)\n", "    plt.barh(top_features['Feature'], top_features['Importance'])\n", "    plt.xlabel('Feature Importance')\n", "    plt.title(f'Top 10 Feature Importances - {best_model_name}')\n", "    plt.gca().invert_yaxis()\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"✓ Feature importance analysis completed\")\n", "else:\n", "    print(f\"\\n⚠ Feature importance not available for {best_model_name}\")"]}, {"cell_type": "markdown", "id": "32", "metadata": {}, "source": ["## 9. Model Saving and Summary"]}, {"cell_type": "code", "execution_count": null, "id": "33", "metadata": {}, "outputs": [], "source": ["# Save model artifacts\n", "print(\"Saving model artifacts...\")\n", "\n", "# Create directory\n", "os.makedirs('enhanced_models', exist_ok=True)\n", "\n", "# Save best model and preprocessing components\n", "joblib.dump(best_model, 'enhanced_models/best_model.joblib')\n", "joblib.dump(scaler, 'enhanced_models/scaler.joblib')\n", "joblib.dump(selector, 'enhanced_models/feature_selector.joblib')\n", "\n", "# Save feature information\n", "pd.Series(selected_features).to_csv('enhanced_models/selected_features.csv', index=False, header=['Feature'])\n", "pd.Series(feature_columns).to_csv('enhanced_models/all_features.csv', index=False, header=['Feature'])\n", "\n", "# Save model performance summary\n", "results_df.to_csv('enhanced_models/model_performance.csv', index=False)\n", "\n", "# Save metadata\n", "metadata = {\n", "    'best_model': best_model_name,\n", "    'best_accuracy': best_accuracy,\n", "    'best_roc_auc': best_roc_auc,\n", "    'original_accuracy': original_accuracy,\n", "    'original_roc_auc': original_roc_auc,\n", "    'accuracy_improvement': accuracy_improvement,\n", "    'roc_improvement': roc_improvement,\n", "    'total_features': len(feature_columns),\n", "    'selected_features': len(selected_features),\n", "    'training_samples': len(X_train),\n", "    'test_samples': len(X_test),\n", "    'target_achieved': best_accuracy > 0.85\n", "}\n", "\n", "pd.Series(metadata).to_csv('enhanced_models/model_metadata.csv', header=['Value'])\n", "\n", "print(\"✓ Model artifacts saved to 'enhanced_models/' directory\")\n", "print(\"  - best_model.joblib (trained model)\")\n", "print(\"  - scaler.joblib (feature scaler)\")\n", "print(\"  - feature_selector.joblib (feature selector)\")\n", "print(\"  - selected_features.csv (selected feature names)\")\n", "print(\"  - model_performance.csv (all model results)\")\n", "print(\"  - model_metadata.csv (experiment metadata)\")"]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["# Final Summary\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"🎯 ENHANCED SME LOAN DEFAULT PREDICTION - FINAL SUMMARY\")\n", "print(\"=\" * 80)\n", "\n", "print(f\"\\n📊 PERFORMANCE ACHIEVEMENTS:\")\n", "print(f\"   • Best Model: {best_model_name}\")\n", "print(f\"   • Accuracy: {best_accuracy:.1%} (Target: 85%)\")\n", "print(f\"   • ROC-AUC: {best_roc_auc:.3f} (Target: 0.85)\")\n", "print(f\"   • Improvement: +{accuracy_improvement:.1f}% accuracy, +{roc_improvement:.1f}% ROC-AUC\")\n", "\n", "print(f\"\\n🔧 TECHNICAL IMPROVEMENTS:\")\n", "print(f\"   • Features: {len(feature_columns)} total, {len(selected_features)} selected\")\n", "print(f\"   • Models: {len(models)} individual + 2 ensemble models\")\n", "print(f\"   • Sampling: BorderlineSMOTE for class imbalance\")\n", "print(f\"   • Validation: Stratified cross-validation\")\n", "print(f\"   • Selection: Statistical feature selection\")\n", "\n", "print(f\"\\n✅ KEY ENHANCEMENTS IMPLEMENTED:\")\n", "enhancements = [\n", "    \"Advanced feature engineering (40+ features)\",\n", "    \"Altman Z-Score and financial health indicators\",\n", "    \"Industry benchmarking and relative metrics\",\n", "    \"XGBoost and LightGBM models (if available)\",\n", "    \"Ensemble methods (Voting + Stacking)\",\n", "    \"BorderlineSMOTE for better class balance\",\n", "    \"Statistical feature selection\",\n", "    \"Comprehensive cross-validation\"\n", "]\n", "\n", "for i, enhancement in enumerate(enhancements, 1):\n", "    print(f\"   {i}. {enhancement}\")\n", "\n", "if best_accuracy > 0.85:\n", "    print(f\"\\n🎉 SUCCESS: Target accuracy of 85% achieved!\")\n", "    print(f\"   The enhanced model shows significant improvement over the original 60% accuracy.\")\n", "else:\n", "    print(f\"\\n📈 PROGRESS: Substantial improvement achieved\")\n", "    print(f\"   Consider further hyperparameter tuning or additional features for 85% target.\")\n", "\n", "print(f\"\\n💾 All model artifacts saved in 'enhanced_models/' directory\")\n", "print(f\"🔄 Ready for production deployment or further optimization\")\n", "print(\"=\" * 80)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}