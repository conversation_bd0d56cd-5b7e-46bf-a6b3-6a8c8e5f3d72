"""
<PERSON><PERSON><PERSON> to install requirements and run the enhanced SME loan default prediction model
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing enhanced requirements...")
    
    # List of packages to install
    packages = [
        'xgboost>=1.5.0',
        'lightgbm>=3.3.0',
        'optuna>=2.10.0',
        'plotly>=5.0.0'
    ]
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✓ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"⚠ Warning: Could not install {package}. Error: {e}")
            print("The model will still work but may have reduced performance.")

def run_enhanced_model():
    """Run the enhanced model"""
    print("\nRunning enhanced SME loan default prediction model...")
    
    try:
        # Import and run the enhanced model
        from enhanced_model_building import main
        main()
    except ImportError as e:
        print(f"Error importing enhanced model: {e}")
        print("Please ensure all files are in the correct location.")
    except Exception as e:
        print(f"Error running enhanced model: {e}")

def main():
    """Main execution"""
    print("SME Loan Default Prediction - Enhanced Model Setup")
    print("="*60)
    
    # Check if data directory exists
    if not os.path.exists('data'):
        print("Error: 'data' directory not found!")
        print("Please ensure you have run the data synthesis notebook first.")
        return
    
    # Check if data files exist
    required_files = [
        'data/sme_profiles.csv',
        'data/sme_financials.csv', 
        'data/sme_alternative_data.csv',
        'data/sme_loan_applications.csv'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print("Error: Missing required data files:")
        for f in missing_files:
            print(f"  - {f}")
        print("Please run the data synthesis notebook first.")
        return
    
    # Install requirements
    install_requirements()
    
    # Run enhanced model
    run_enhanced_model()
    
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    print("Check the 'enhanced_models/' directory for saved model artifacts.")
    print("Review the console output for performance metrics.")

if __name__ == "__main__":
    main()
