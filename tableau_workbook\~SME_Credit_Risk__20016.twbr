<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20251.25.0415.1018                               -->
<workbook original-version='18.1' source-build='2025.1.1 (20251.25.0415.1018)' source-platform='win' version='18.1' xml:base='https://public.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <AccessibleZoneTabOrder />
    <AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <IntuitiveSorting />
    <IntuitiveSorting_SP2 />
    <MarkAnimation />
    <ObjectModelEncapsulateLegacy />
    <ObjectModelExtractV2 />
    <ObjectModelTableType />
    <SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <_.fcp.VConnDownstreamExtractsWithWarnings.true...VConnDownstreamExtractsWithWarnings />
    <WindowsPersistSimpleIdentifiers />
  </document-format-change-manifest>
  <repository-location id='SME_Credit_Risk' path='/workbooks' revision='1.3' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <datasources>
    <datasource caption='tableau_combined_data' inline='true' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' version='18.1'>
      <connection class='federated'>
        <named-connections>
          <named-connection caption='tableau_combined_data' name='textscan.19iy8t31e5xux119q25zd0tdpvfn'>
            <connection class='textscan' cleaning='yes' compat='no' csvFile='E:\Codecademy\DA_projects\1\data\tableau_combined_data.csv' dataRefreshTime='' directory='E:/Codecademy/DA_projects/1/data' filename='C:/Users/<USER>/AppData/Local/Temp/TableauTemp/09tr0tf0ohlcaj0zn9pn70ot6eku/tableau_combined_data.csv.xlsx' interpretationMode='8' password='' server='' validate='no' workgroup-auth-mode='as-is' />
          </named-connection>
        </named-connections>
        <relation connection='textscan.19iy8t31e5xux119q25zd0tdpvfn' name='tableau_combined_data.csv' table='[E:\Codecademy\DA_projects\1\data\tableau_combined_data#csv]' type='table'>
          <columns character-set='UTF-8' gridOrigin='A1:AE992:no:A1:AE992:1' header='yes' locale='en_US' separator=','>
            <column datatype='integer' name='Loan_Application_ID' ordinal='0' />
            <column datatype='integer' name='SME_ID' ordinal='1' />
            <column datatype='date' name='Loan_Application_Date' ordinal='2' />
            <column datatype='real' name='Loan_Amount_Requested' ordinal='3' />
            <column datatype='real' name='Default_Probability' ordinal='4' />
            <column datatype='integer' name='Loan_Default_Status' ordinal='5' />
            <column datatype='date' name='Observation_EndDate' ordinal='6' />
            <column datatype='string' name='SME_Name' ordinal='7' />
            <column datatype='string' name='Industry_Sector' ordinal='8' />
            <column datatype='integer' name='Years_In_Business' ordinal='9' />
            <column datatype='string' name='Geographic_Location' ordinal='10' />
            <column datatype='integer' name='Geographic_Location_Risk_Tier' ordinal='11' />
            <column datatype='integer' name='Financial_Record_ID' ordinal='12' />
            <column datatype='integer' name='Year' ordinal='13' />
            <column datatype='real' name='Revenue' ordinal='14' />
            <column datatype='real' name='COGS' ordinal='15' />
            <column datatype='real' name='Operating_Expense' ordinal='16' />
            <column datatype='real' name='Net_Income' ordinal='17' />
            <column datatype='real' name='Total_Assets' ordinal='18' />
            <column datatype='real' name='Cash_Holdings' ordinal='19' />
            <column datatype='real' name='Accounts_Receivable' ordinal='20' />
            <column datatype='real' name='Inventory' ordinal='21' />
            <column datatype='real' name='Total_Liabilities' ordinal='22' />
            <column datatype='real' name='Accounts_Payable' ordinal='23' />
            <column datatype='real' name='Equity' ordinal='24' />
            <column datatype='integer' name='Alt_Data_ID' ordinal='25' />
            <column datatype='real' name='Online_Presence_Score' ordinal='26' />
            <column datatype='real' name='Average_Customer_Review_Score' ordinal='27' />
            <column datatype='integer' name='Number_of_Customer_Complaints_Monthly' ordinal='28' />
            <column datatype='real' name='Payment_Gateway_Transaction_Volatility' ordinal='29' />
            <column datatype='string' name='Risk_Category' ordinal='30' />
          </columns>
        </relation>
        <metadata-records>
          <metadata-record class='capability'>
            <remote-name />
            <remote-type>0</remote-type>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias />
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='character-set'>&quot;UTF-8&quot;</attribute>
              <attribute datatype='string' name='collation'>&quot;en_US&quot;</attribute>
              <attribute datatype='string' name='field-delimiter'>&quot;,&quot;</attribute>
              <attribute datatype='string' name='gridOrigin'>&quot;A1:AE992:no:A1:AE992:1&quot;</attribute>
              <attribute datatype='string' name='header-row'>&quot;true&quot;</attribute>
              <attribute datatype='string' name='locale'>&quot;en_US&quot;</attribute>
              <attribute datatype='string' name='single-char'>&quot;&quot;</attribute>
            </attributes>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Loan_Application_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Loan_Application_ID]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Loan_Application_ID</remote-alias>
            <ordinal>0</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SME_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[SME_ID]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>SME_ID</remote-alias>
            <ordinal>1</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Loan_Application_Date</remote-name>
            <remote-type>133</remote-type>
            <local-name>[Loan_Application_Date]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Loan_Application_Date</remote-alias>
            <ordinal>2</ordinal>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Loan_Amount_Requested</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Loan_Amount_Requested]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Loan_Amount_Requested</remote-alias>
            <ordinal>3</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Default_Probability</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Default_Probability]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Default_Probability</remote-alias>
            <ordinal>4</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Loan_Default_Status</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Loan_Default_Status]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Loan_Default_Status</remote-alias>
            <ordinal>5</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Observation_EndDate</remote-name>
            <remote-type>133</remote-type>
            <local-name>[Observation_EndDate]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Observation_EndDate</remote-alias>
            <ordinal>6</ordinal>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SME_Name</remote-name>
            <remote-type>129</remote-type>
            <local-name>[SME_Name]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>SME_Name</remote-alias>
            <ordinal>7</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>**********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Industry_Sector</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Industry_Sector]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Industry_Sector</remote-alias>
            <ordinal>8</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>**********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Years_In_Business</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Years_In_Business]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Years_In_Business</remote-alias>
            <ordinal>9</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Geographic_Location</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Geographic_Location]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Geographic_Location</remote-alias>
            <ordinal>10</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>**********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Geographic_Location_Risk_Tier</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Geographic_Location_Risk_Tier]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Geographic_Location_Risk_Tier</remote-alias>
            <ordinal>11</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Financial_Record_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Financial_Record_ID]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Financial_Record_ID</remote-alias>
            <ordinal>12</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Year</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Year]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Year</remote-alias>
            <ordinal>13</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Revenue</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Revenue]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Revenue</remote-alias>
            <ordinal>14</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>COGS</remote-name>
            <remote-type>5</remote-type>
            <local-name>[COGS]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>COGS</remote-alias>
            <ordinal>15</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Operating_Expense</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Operating_Expense]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Operating_Expense</remote-alias>
            <ordinal>16</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Net_Income</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Net_Income]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Net_Income</remote-alias>
            <ordinal>17</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Total_Assets</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Total_Assets]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Total_Assets</remote-alias>
            <ordinal>18</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Cash_Holdings</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Cash_Holdings]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Cash_Holdings</remote-alias>
            <ordinal>19</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Accounts_Receivable</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Accounts_Receivable]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Accounts_Receivable</remote-alias>
            <ordinal>20</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Inventory</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Inventory]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Inventory</remote-alias>
            <ordinal>21</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Total_Liabilities</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Total_Liabilities]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Total_Liabilities</remote-alias>
            <ordinal>22</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Accounts_Payable</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Accounts_Payable]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Accounts_Payable</remote-alias>
            <ordinal>23</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Equity</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Equity]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Equity</remote-alias>
            <ordinal>24</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Alt_Data_ID</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Alt_Data_ID]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Alt_Data_ID</remote-alias>
            <ordinal>25</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Online_Presence_Score</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Online_Presence_Score]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Online_Presence_Score</remote-alias>
            <ordinal>26</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Average_Customer_Review_Score</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Average_Customer_Review_Score]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Average_Customer_Review_Score</remote-alias>
            <ordinal>27</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Number_of_Customer_Complaints_Monthly</remote-name>
            <remote-type>20</remote-type>
            <local-name>[Number_of_Customer_Complaints_Monthly]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Number_of_Customer_Complaints_Monthly</remote-alias>
            <ordinal>28</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Payment_Gateway_Transaction_Volatility</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Payment_Gateway_Transaction_Volatility]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Payment_Gateway_Transaction_Volatility</remote-alias>
            <ordinal>29</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <contains-null>true</contains-null>
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Risk_Category</remote-name>
            <remote-type>129</remote-type>
            <local-name>[Risk_Category]</local-name>
            <parent-name>[tableau_combined_data.csv]</parent-name>
            <remote-alias>Risk_Category</remote-alias>
            <ordinal>30</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <scale>1</scale>
            <width>**********</width>
            <contains-null>true</contains-null>
            <collation flag='0' name='LEN_RUS' />
            <object-id>[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]</object-id>
          </metadata-record>
        </metadata-records>
      </connection>
      <aliases enabled='yes' />
      <column caption='Accounts Payable' datatype='real' name='[Accounts_Payable]' role='measure' type='quantitative' />
      <column caption='Accounts Receivable' datatype='real' name='[Accounts_Receivable]' role='measure' type='quantitative' />
      <column caption='Alt Data ID' datatype='integer' name='[Alt_Data_ID]' role='dimension' type='ordinal' />
      <column caption='Average Customer Review Score' datatype='real' name='[Average_Customer_Review_Score]' role='measure' type='quantitative' />
      <column caption='Cogs' datatype='real' name='[COGS]' role='measure' type='quantitative' />
      <column caption='Debt to Equity' datatype='real' name='[Calculation_1408500792579899392]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='[Total_Liabilities]/[Equity]' />
      </column>
      <column caption='Profit Margin' datatype='real' name='[Calculation_1408500792580079617]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='[Net_Income]/[Revenue]' />
      </column>
      <column caption='Liquidity Ratio' datatype='real' name='[Calculation_1408500792580288514]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='[Cash_Holdings]/[Total_Liabilities]' />
      </column>
      <column caption='EBITDA' datatype='real' name='[Calculation_1408500792580612099]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='[Revenue]-[COGS]' />
      </column>
      <column caption='Cash Holdings' datatype='real' name='[Cash_Holdings]' role='measure' type='quantitative' />
      <column caption='Default Probability' datatype='real' name='[Default_Probability]' role='measure' type='quantitative' />
      <column caption='Financial Record ID' datatype='integer' name='[Financial_Record_ID]' role='dimension' type='ordinal' />
      <column caption='Geographic Location' datatype='string' name='[Geographic_Location]' role='dimension' type='nominal' />
      <column caption='Geographic Location Risk Tier' datatype='integer' name='[Geographic_Location_Risk_Tier]' role='measure' type='quantitative' />
      <column caption='Industry Sector' datatype='string' name='[Industry_Sector]' role='dimension' type='nominal' />
      <column caption='Loan Amount Requested' datatype='real' name='[Loan_Amount_Requested]' role='measure' type='quantitative' />
      <column caption='Loan Application Date' datatype='date' name='[Loan_Application_Date]' role='dimension' type='ordinal' />
      <column caption='Loan Application ID' datatype='integer' name='[Loan_Application_ID]' role='dimension' type='ordinal' />
      <column caption='Loan Default Status' datatype='integer' name='[Loan_Default_Status]' role='dimension' type='ordinal'>
        <aliases>
          <alias key='0' value='No Default' />
          <alias key='1' value='Default' />
        </aliases>
      </column>
      <column caption='Net Income' datatype='real' name='[Net_Income]' role='measure' type='quantitative' />
      <column caption='Number of Customer Complaints Monthly' datatype='integer' name='[Number_of_Customer_Complaints_Monthly]' role='measure' type='quantitative' />
      <column caption='Observation EndDate' datatype='date' name='[Observation_EndDate]' role='dimension' type='ordinal' />
      <column caption='Online Presence Score' datatype='real' name='[Online_Presence_Score]' role='measure' type='quantitative' />
      <column caption='Operating Expense' datatype='real' name='[Operating_Expense]' role='measure' type='quantitative' />
      <column caption='Payment Gateway Transaction Volatility' datatype='real' name='[Payment_Gateway_Transaction_Volatility]' role='measure' type='quantitative' />
      <column caption='Risk Category' datatype='string' name='[Risk_Category]' role='dimension' type='nominal' />
      <column caption='Sme Id' datatype='integer' name='[SME_ID]' role='dimension' type='ordinal' />
      <column caption='SME Name' datatype='string' name='[SME_Name]' role='dimension' type='nominal' />
      <column caption='Total Assets' datatype='real' name='[Total_Assets]' role='measure' type='quantitative' />
      <column caption='Total Liabilities' datatype='real' name='[Total_Liabilities]' role='measure' type='quantitative' />
      <column datatype='integer' name='[Year]' role='dimension' type='quantitative' />
      <column caption='Years In Business' datatype='integer' name='[Years_In_Business]' role='measure' type='quantitative' />
      <column caption='tableau_combined_data.csv' datatype='table' name='[__tableau_internal_object_id__].[tableau_combined_data.csv_94C7DFB091984DE19820022D19885342]' role='measure' type='quantitative' />
      <extract _.fcp.VConnDownstreamExtractsWithWarnings.true...user-specific='false' count='-1' enabled='true' object-id='' units='records'>
        <connection access_mode='readonly' author-locale='en_US' class='hyper' dbname='Data/TableauTemp/#TableauTemp_0nq2j401vagz8c1a6ra3f1mz679k.hyper' default-settings='hyper' schema='Extract' sslmode='' tablename='Extract' update-time='' username='tableau_internal_user'>
          <relation name='Extract' table='[Extract].[Extract]' type='table' />
          <metadata-records>
            <metadata-record class='column'>
              <remote-name>Loan_Application_ID</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Loan_Application_ID]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Loan_Application_ID</remote-alias>
              <ordinal>0</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>991</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>SME_ID</remote-name>
              <remote-type>20</remote-type>
              <local-name>[SME_ID]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>SME_ID</remote-alias>
              <ordinal>1</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Loan_Application_Date</remote-name>
              <remote-type>133</remote-type>
              <local-name>[Loan_Application_Date]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Loan_Application_Date</remote-alias>
              <ordinal>2</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>date</local-type>
              <aggregation>Year</aggregation>
              <approx-count>695</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Loan_Amount_Requested</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Loan_Amount_Requested]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Loan_Amount_Requested</remote-alias>
              <ordinal>3</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>991</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Default_Probability</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Default_Probability]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Default_Probability</remote-alias>
              <ordinal>4</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>959</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Loan_Default_Status</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Loan_Default_Status]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Loan_Default_Status</remote-alias>
              <ordinal>5</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>2</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Observation_EndDate</remote-name>
              <remote-type>133</remote-type>
              <local-name>[Observation_EndDate]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Observation_EndDate</remote-alias>
              <ordinal>6</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>date</local-type>
              <aggregation>Year</aggregation>
              <approx-count>695</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>SME_Name</remote-name>
              <remote-type>129</remote-type>
              <local-name>[SME_Name]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>SME_Name</remote-alias>
              <ordinal>7</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>string</local-type>
              <aggregation>Count</aggregation>
              <approx-count>495</approx-count>
              <contains-null>true</contains-null>
              <collation flag='0' name='LEN_RUS' />
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Industry_Sector</remote-name>
              <remote-type>129</remote-type>
              <local-name>[Industry_Sector]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Industry_Sector</remote-alias>
              <ordinal>8</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>string</local-type>
              <aggregation>Count</aggregation>
              <approx-count>6</approx-count>
              <contains-null>true</contains-null>
              <collation flag='0' name='LEN_RUS' />
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Years_In_Business</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Years_In_Business]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Years_In_Business</remote-alias>
              <ordinal>9</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>20</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Geographic_Location</remote-name>
              <remote-type>129</remote-type>
              <local-name>[Geographic_Location]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Geographic_Location</remote-alias>
              <ordinal>10</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>string</local-type>
              <aggregation>Count</aggregation>
              <approx-count>3</approx-count>
              <contains-null>true</contains-null>
              <collation flag='0' name='LEN_RUS' />
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Geographic_Location_Risk_Tier</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Geographic_Location_Risk_Tier]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Geographic_Location_Risk_Tier</remote-alias>
              <ordinal>11</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>3</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Financial_Record_ID</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Financial_Record_ID]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Financial_Record_ID</remote-alias>
              <ordinal>12</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Year</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Year]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Year</remote-alias>
              <ordinal>13</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>1</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Revenue</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Revenue]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Revenue</remote-alias>
              <ordinal>14</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>COGS</remote-name>
              <remote-type>5</remote-type>
              <local-name>[COGS]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>COGS</remote-alias>
              <ordinal>15</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Operating_Expense</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Operating_Expense]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Operating_Expense</remote-alias>
              <ordinal>16</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Net_Income</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Net_Income]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Net_Income</remote-alias>
              <ordinal>17</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Total_Assets</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Total_Assets]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Total_Assets</remote-alias>
              <ordinal>18</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Cash_Holdings</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Cash_Holdings]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Cash_Holdings</remote-alias>
              <ordinal>19</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Accounts_Receivable</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Accounts_Receivable]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Accounts_Receivable</remote-alias>
              <ordinal>20</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Inventory</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Inventory]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Inventory</remote-alias>
              <ordinal>21</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Total_Liabilities</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Total_Liabilities]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Total_Liabilities</remote-alias>
              <ordinal>22</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Accounts_Payable</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Accounts_Payable]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Accounts_Payable</remote-alias>
              <ordinal>23</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Equity</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Equity]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Equity</remote-alias>
              <ordinal>24</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Alt_Data_ID</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Alt_Data_ID]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Alt_Data_ID</remote-alias>
              <ordinal>25</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Online_Presence_Score</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Online_Presence_Score]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Online_Presence_Score</remote-alias>
              <ordinal>26</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>437</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Average_Customer_Review_Score</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Average_Customer_Review_Score]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Average_Customer_Review_Score</remote-alias>
              <ordinal>27</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Number_of_Customer_Complaints_Monthly</remote-name>
              <remote-type>20</remote-type>
              <local-name>[Number_of_Customer_Complaints_Monthly]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Number_of_Customer_Complaints_Monthly</remote-alias>
              <ordinal>28</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>integer</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>12</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Payment_Gateway_Transaction_Volatility</remote-name>
              <remote-type>5</remote-type>
              <local-name>[Payment_Gateway_Transaction_Volatility]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Payment_Gateway_Transaction_Volatility</remote-alias>
              <ordinal>29</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>real</local-type>
              <aggregation>Sum</aggregation>
              <approx-count>500</approx-count>
              <contains-null>true</contains-null>
            </metadata-record>
            <metadata-record class='column'>
              <remote-name>Risk_Category</remote-name>
              <remote-type>129</remote-type>
              <local-name>[Risk_Category]</local-name>
              <parent-name>[Extract]</parent-name>
              <remote-alias>Risk_Category</remote-alias>
              <ordinal>30</ordinal>
              <family>tableau_combined_data.csv</family>
              <local-type>string</local-type>
              <aggregation>Count</aggregation>
              <approx-count>4</approx-count>
              <contains-null>true</contains-null>
              <collation flag='0' name='LEN_RUS' />
            </metadata-record>
          </metadata-records>
        </connection>
      </extract>
      <layout dim-ordering='alphabetic' measure-ordering='alphabetic' show-structure='true' />
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;India&quot;' />
      </semantic-values>
      <object-graph>
        <objects>
          <object caption='tableau_combined_data.csv' id='tableau_combined_data.csv_94C7DFB091984DE19820022D19885342'>
            <properties context=''>
              <relation connection='textscan.19iy8t31e5xux119q25zd0tdpvfn' name='tableau_combined_data.csv' table='[E:\Codecademy\DA_projects\1\data\tableau_combined_data#csv]' type='table'>
                <columns character-set='UTF-8' gridOrigin='A1:AE992:no:A1:AE992:1' header='yes' locale='en_US' separator=','>
                  <column datatype='integer' name='Loan_Application_ID' ordinal='0' />
                  <column datatype='integer' name='SME_ID' ordinal='1' />
                  <column datatype='date' name='Loan_Application_Date' ordinal='2' />
                  <column datatype='real' name='Loan_Amount_Requested' ordinal='3' />
                  <column datatype='real' name='Default_Probability' ordinal='4' />
                  <column datatype='integer' name='Loan_Default_Status' ordinal='5' />
                  <column datatype='date' name='Observation_EndDate' ordinal='6' />
                  <column datatype='string' name='SME_Name' ordinal='7' />
                  <column datatype='string' name='Industry_Sector' ordinal='8' />
                  <column datatype='integer' name='Years_In_Business' ordinal='9' />
                  <column datatype='string' name='Geographic_Location' ordinal='10' />
                  <column datatype='integer' name='Geographic_Location_Risk_Tier' ordinal='11' />
                  <column datatype='integer' name='Financial_Record_ID' ordinal='12' />
                  <column datatype='integer' name='Year' ordinal='13' />
                  <column datatype='real' name='Revenue' ordinal='14' />
                  <column datatype='real' name='COGS' ordinal='15' />
                  <column datatype='real' name='Operating_Expense' ordinal='16' />
                  <column datatype='real' name='Net_Income' ordinal='17' />
                  <column datatype='real' name='Total_Assets' ordinal='18' />
                  <column datatype='real' name='Cash_Holdings' ordinal='19' />
                  <column datatype='real' name='Accounts_Receivable' ordinal='20' />
                  <column datatype='real' name='Inventory' ordinal='21' />
                  <column datatype='real' name='Total_Liabilities' ordinal='22' />
                  <column datatype='real' name='Accounts_Payable' ordinal='23' />
                  <column datatype='real' name='Equity' ordinal='24' />
                  <column datatype='integer' name='Alt_Data_ID' ordinal='25' />
                  <column datatype='real' name='Online_Presence_Score' ordinal='26' />
                  <column datatype='real' name='Average_Customer_Review_Score' ordinal='27' />
                  <column datatype='integer' name='Number_of_Customer_Complaints_Monthly' ordinal='28' />
                  <column datatype='real' name='Payment_Gateway_Transaction_Volatility' ordinal='29' />
                  <column datatype='string' name='Risk_Category' ordinal='30' />
                </columns>
              </relation>
            </properties>
            <properties context='extract'>
              <relation name='Extract' table='[Extract].[Extract]' type='table' />
            </properties>
          </object>
        </objects>
      </object-graph>
    </datasource>
  </datasources>
  <worksheets>
    <worksheet name='Default Probability by Debt to Equity Ratio'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true'>Default Probability by Debt to Equity Ratio</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Debt to Equity' datatype='real' name='[Calculation_1408500792579899392]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='[Total_Liabilities]/[Equity]' />
            </column>
            <column caption='Default Probability' datatype='real' name='[Default_Probability]' role='measure' type='quantitative' />
            <column datatype='real' name='[Equity]' role='measure' type='quantitative' />
            <column caption='Loan Amount Requested' datatype='real' name='[Loan_Amount_Requested]' role='measure' type='quantitative' />
            <column caption='Loan Default Status' datatype='integer' name='[Loan_Default_Status]' role='dimension' type='ordinal'>
              <aliases>
                <alias key='0' value='No Default' />
                <alias key='1' value='Default' />
              </aliases>
            </column>
            <column caption='Total Liabilities' datatype='real' name='[Total_Liabilities]' role='measure' type='quantitative' />
            <column-instance column='[Calculation_1408500792579899392]' derivation='Avg' name='[avg:Calculation_1408500792579899392:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Default_Probability]' derivation='None' name='[none:Default_Probability:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Loan_Default_Status]' derivation='None' name='[none:Loan_Default_Status:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Loan_Amount_Requested]' derivation='Sum' name='[sum:Loan_Amount_Requested:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Circle' />
            <encodings>
              <color column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' />
              <size column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' />
            </encodings>
            <trendline enable-confidence-bands='false' enable-instant-analytics='true' enabled='true' exclude-color='false' exclude-intercept='false' fit='linear' />
          </pane>
        </panes>
        <rows>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Calculation_1408500792579899392:qk]</rows>
        <cols>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Default_Probability:qk]</cols>
      </table>
      <simple-id uuid='{5D26C39B-6634-4E94-B4E6-CB055674E033}' />
    </worksheet>
    <worksheet name='Default Probability by EBITDA'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontsize='16'>Default Probability by EBITDA</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Cogs' datatype='real' name='[COGS]' role='measure' type='quantitative' />
            <column caption='EBITDA' datatype='real' name='[Calculation_1408500792580612099]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='[Revenue]-[COGS]' />
            </column>
            <column caption='Default Probability' datatype='real' name='[Default_Probability]' role='measure' type='quantitative' />
            <column caption='Loan Amount Requested' datatype='real' name='[Loan_Amount_Requested]' role='measure' type='quantitative' />
            <column caption='Loan Default Status' datatype='integer' name='[Loan_Default_Status]' role='dimension' type='ordinal'>
              <aliases>
                <alias key='0' value='No Default' />
                <alias key='1' value='Default' />
              </aliases>
            </column>
            <column datatype='real' name='[Revenue]' role='measure' type='quantitative' />
            <column-instance column='[Calculation_1408500792580612099]' derivation='Avg' name='[avg:Calculation_1408500792580612099:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Default_Probability]' derivation='None' name='[none:Default_Probability:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Loan_Default_Status]' derivation='None' name='[none:Loan_Default_Status:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Loan_Amount_Requested]' derivation='Sum' name='[sum:Loan_Amount_Requested:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Circle' />
            <encodings>
              <color column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' />
              <size column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' />
            </encodings>
            <trendline enable-confidence-bands='false' enable-instant-analytics='true' enabled='true' exclude-color='false' exclude-intercept='false' fit='linear' />
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-show' value='false' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Calculation_1408500792580612099:qk]</rows>
        <cols>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Default_Probability:qk]</cols>
      </table>
      <simple-id uuid='{00E4F5A0-A092-46AC-A2B9-FE8FBB1F5E05}' />
    </worksheet>
    <worksheet name='Default Probability by Liquidity Ratio'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true'>Default Probability by Liquidity Ratio</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Liquidity Ratio' datatype='real' name='[Calculation_1408500792580288514]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='[Cash_Holdings]/[Total_Liabilities]' />
            </column>
            <column caption='Cash Holdings' datatype='real' name='[Cash_Holdings]' role='measure' type='quantitative' />
            <column caption='Default Probability' datatype='real' name='[Default_Probability]' role='measure' type='quantitative' />
            <column caption='Loan Amount Requested' datatype='real' name='[Loan_Amount_Requested]' role='measure' type='quantitative' />
            <column caption='Loan Default Status' datatype='integer' name='[Loan_Default_Status]' role='dimension' type='ordinal'>
              <aliases>
                <alias key='0' value='No Default' />
                <alias key='1' value='Default' />
              </aliases>
            </column>
            <column caption='Total Liabilities' datatype='real' name='[Total_Liabilities]' role='measure' type='quantitative' />
            <column-instance column='[Calculation_1408500792580288514]' derivation='Avg' name='[avg:Calculation_1408500792580288514:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Default_Probability]' derivation='None' name='[none:Default_Probability:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Loan_Default_Status]' derivation='None' name='[none:Loan_Default_Status:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Loan_Amount_Requested]' derivation='Sum' name='[sum:Loan_Amount_Requested:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Circle' />
            <encodings>
              <color column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' />
              <size column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' />
            </encodings>
            <trendline enable-confidence-bands='false' enable-instant-analytics='true' enabled='true' exclude-color='false' exclude-intercept='false' fit='linear' />
          </pane>
        </panes>
        <rows>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Calculation_1408500792580288514:qk]</rows>
        <cols>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Default_Probability:qk]</cols>
      </table>
      <simple-id uuid='{9D3087F6-7D0F-4536-A640-EAF6406C16EC}' />
    </worksheet>
    <worksheet name='Default Probability by Profit Margin'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true'>Default Probability by Profit Margin</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Profit Margin' datatype='real' name='[Calculation_1408500792580079617]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='[Net_Income]/[Revenue]' />
            </column>
            <column caption='Default Probability' datatype='real' name='[Default_Probability]' role='measure' type='quantitative' />
            <column caption='Loan Amount Requested' datatype='real' name='[Loan_Amount_Requested]' role='measure' type='quantitative' />
            <column caption='Loan Default Status' datatype='integer' name='[Loan_Default_Status]' role='dimension' type='ordinal'>
              <aliases>
                <alias key='0' value='No Default' />
                <alias key='1' value='Default' />
              </aliases>
            </column>
            <column caption='Net Income' datatype='real' name='[Net_Income]' role='measure' type='quantitative' />
            <column datatype='real' name='[Revenue]' role='measure' type='quantitative' />
            <column-instance column='[Calculation_1408500792580079617]' derivation='Avg' name='[avg:Calculation_1408500792580079617:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Default_Probability]' derivation='None' name='[none:Default_Probability:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Loan_Default_Status]' derivation='None' name='[none:Loan_Default_Status:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Loan_Amount_Requested]' derivation='Sum' name='[sum:Loan_Amount_Requested:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Circle' />
            <encodings>
              <color column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' />
              <size column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' />
            </encodings>
            <trendline enable-confidence-bands='false' enable-instant-analytics='true' enabled='true' exclude-color='false' exclude-intercept='false' fit='linear' />
          </pane>
        </panes>
        <rows>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Calculation_1408500792580079617:qk]</rows>
        <cols>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Default_Probability:qk]</cols>
      </table>
      <simple-id uuid='{48659B80-ACE2-4602-93E3-B49E85FF8D96}' />
    </worksheet>
    <worksheet name='Default Rate By Business'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true'>Default Rate by Number of Years in Business</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Loan Default Status' datatype='integer' name='[Loan_Default_Status]' role='dimension' type='ordinal'>
              <aliases>
                <alias key='0' value='No Default' />
                <alias key='1' value='Default' />
              </aliases>
            </column>
            <column caption='Years In Business' datatype='integer' name='[Years_In_Business]' role='measure' type='quantitative' />
            <column-instance column='[Loan_Default_Status]' derivation='Avg' name='[avg:Loan_Default_Status:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Years_In_Business]' derivation='None' name='[none:Years_In_Business:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='header'>
            <format attr='height-header' value='10' />
          </style-rule>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Area' />
            <trendline enable-confidence-bands='false' enable-instant-analytics='true' enabled='true' exclude-color='false' exclude-intercept='false' fit='linear' />
          </pane>
        </panes>
        <rows>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Loan_Default_Status:qk]</rows>
        <cols>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Years_In_Business:qk]</cols>
      </table>
      <simple-id uuid='{A11038A8-6AF4-419A-8A80-8ADA337646E0}' />
    </worksheet>
    <worksheet name='Default Rate by Industry'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true'>Default Rate by Industry</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Industry Sector' datatype='string' name='[Industry_Sector]' role='dimension' type='nominal' />
            <column caption='Loan Default Status' datatype='integer' name='[Loan_Default_Status]' role='dimension' type='ordinal'>
              <aliases>
                <alias key='0' value='No Default' />
                <alias key='1' value='Default' />
              </aliases>
            </column>
            <column-instance column='[Loan_Default_Status]' derivation='Avg' name='[avg:Loan_Default_Status:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Industry_Sector]' derivation='None' name='[none:Industry_Sector:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <shelf-sorts>
            <shelf-sort-v2 dimension-to-sort='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Industry_Sector:nk]' direction='DESC' is-on-innermost-dimension='true' measure-to-sort-by='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Loan_Default_Status:qk]' shelf='rows' />
          </shelf-sorts>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <mark-sizing mark-sizing-setting='marks-scaling-off' />
            <encodings>
              <color column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Industry_Sector:nk]' />
              <lod column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Loan_Default_Status:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='size' value='1.0544751882553101' />
                <format attr='mark-labels-show' value='false' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Industry_Sector:nk]</rows>
        <cols>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Loan_Default_Status:qk]</cols>
      </table>
      <simple-id uuid='{5330204E-D7E4-4EE4-9479-133390D6DA55}' />
    </worksheet>
    <worksheet name='Geographic Distribution Map'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true'>Geographic Distribution Map</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Default Probability' datatype='real' name='[Default_Probability]' role='measure' type='quantitative' />
            <column caption='Geographic Location' datatype='string' name='[Geographic_Location]' role='dimension' type='nominal' />
            <column caption='Loan Application ID' datatype='integer' name='[Loan_Application_ID]' role='dimension' type='ordinal' />
            <column-instance column='[Default_Probability]' derivation='Avg' name='[avg:Default_Probability:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Loan_Application_ID]' derivation='Count' name='[cnt:Loan_Application_ID:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Geographic_Location]' derivation='None' name='[none:Geographic_Location:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
            <encodings>
              <color column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Default_Probability:qk]' />
              <size column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[cnt:Loan_Application_ID:qk]' />
              <lod column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Geographic_Location:nk]' />
            </encodings>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{94E5B7C0-6847-40B0-BDC6-BAEC245AB3C3}' />
    </worksheet>
    <worksheet name='Risk Distribution'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true'>Risk Distribution</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='tableau_combined_data' name='federated.060ig3j0nvmkqp16e1g8c1nn1twz' />
          </datasources>
          <datasource-dependencies datasource='federated.060ig3j0nvmkqp16e1g8c1nn1twz'>
            <column caption='Default Probability' datatype='real' name='[Default_Probability]' role='measure' type='quantitative' />
            <column caption='Risk Category' datatype='string' name='[Risk_Category]' role='dimension' type='nominal' />
            <column-instance column='[Default_Probability]' derivation='Avg' name='[avg:Default_Probability:qk]' pivot='key' type='quantitative' />
            <column-instance column='[Risk_Category]' derivation='None' name='[none:Risk_Category:nk]' pivot='key' type='nominal' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='table'>
            <format attr='background-color' value='#faf4e7' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <color column='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Risk_Category:nk]' />
            </encodings>
          </pane>
        </panes>
        <rows>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Risk_Category:nk]</rows>
        <cols>[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Default_Probability:qk]</cols>
      </table>
      <simple-id uuid='{FA911EFD-00B4-462A-95A6-6267F7DE0F70}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard enable-sort-zone-taborder='true' name='Dashboard 1'>
      <style>
        <style-rule element='dash-text'>
          <format attr='text-align' id='dash-text_13' value='center' />
        </style-rule>
      </style>
      <size sizing-mode='automatic' />
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='88042' id='8' param='horz' type-v2='layout-flow' w='99034' x='483' y='10972'>
            <zone h='88042' id='6' type-v2='layout-basic' w='89125' x='483' y='10972'>
              <zone h='44021' id='3' name='Default Rate by Industry' w='44570' x='45038' y='10972'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone h='58915' id='5' name='Geographic Distribution Map' w='44555' x='483' y='10972'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone h='44021' id='10' name='Default Rate By Business' w='44570' x='45038' y='54993'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone h='29127' id='11' name='Risk Distribution' w='44555' x='483' y='69887'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
            </zone>
            <zone fixed-size='164' h='88042' id='14' is-fixed='true' type-v2='layout-basic' w='9909' x='89608' y='10972'>
              <zone h='44019' id='7' param='vert' type-v2='layout-flow' w='9909' x='89608' y='10972'>
                <zone fixed-size='531' h='44019' id='9' is-fixed='true' name='Geographic Distribution Map' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Default_Probability:qk]' type-v2='color' w='9909' x='89608' y='10972'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
              </zone>
              <zone fixed-size='137' h='44023' id='12' is-fixed='true' name='Risk Distribution' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Risk_Category:nk]' type-v2='color' w='9909' x='89608' y='54991'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
            </zone>
          </zone>
          <zone h='9986' id='13' type-v2='text' w='99034' x='483' y='986'>
            <formatted-text>
              <run bold='true' fontcolor='#75a1c7' fontname='Cooper Black' fontsize='16'>SME Loan Default Risk Overview</run>
            </formatted-text>
            <zone-style>
              <format attr='border-color' value='#000000' />
              <format attr='border-style' value='none' />
              <format attr='border-width' value='0' />
              <format attr='margin' value='4' />
              <format attr='background-color' value='#faf4e7' />
            </zone-style>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
          </zone-style>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <size maxheight='1350' minheight='1350' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='24' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98028' id='23' param='vert' type-v2='layout-flow' w='99034' x='483' y='986'>
                <zone h='9986' id='13' type-v2='text' w='99034' x='483' y='986'>
                  <formatted-text>
                    <run bold='true' fontcolor='#75a1c7' fontname='Cooper Black' fontsize='16'>SME Loan Default Risk Overview</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='58915' id='5' is-fixed='true' name='Geographic Distribution Map' w='44555' x='483' y='10972'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='531' h='44019' id='9' name='Geographic Distribution Map' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Default_Probability:qk]' type-v2='color' w='9909' x='89608' y='10972'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='44021' id='3' is-fixed='true' name='Default Rate by Industry' w='44570' x='45038' y='10972'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='44021' id='10' is-fixed='true' name='Default Rate By Business' w='44570' x='45038' y='54993'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='225' h='29127' id='11' is-fixed='true' name='Risk Distribution' w='44555' x='483' y='69887'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='137' h='44023' id='12' name='Risk Distribution' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Risk_Category:nk]' type-v2='color' w='9909' x='89608' y='54991'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{C1A28FA1-ECC9-45A4-B2B6-1D8491B3CD91}' />
    </dashboard>
    <dashboard enable-sort-zone-taborder='true' name='Dashboard 2'>
      <style />
      <size sizing-mode='automatic' />
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='98028' id='7' param='horz' type-v2='layout-flow' w='99034' x='483' y='986'>
            <zone h='98028' id='5' type-v2='layout-basic' w='89366' x='483' y='986'>
              <zone h='49014' id='3' name='Default Probability by EBITDA' w='44683' x='483' y='986'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone h='49014' id='10' name='Default Probability by Debt to Equity Ratio' w='44683' x='45166' y='986'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone h='49014' id='11' name='Default Probability by Profit Margin' w='44683' x='483' y='50000'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone h='49014' id='12' name='Default Probability by Liquidity Ratio' w='44683' x='45166' y='50000'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
            </zone>
            <zone fixed-size='160' h='98028' id='6' is-fixed='true' param='vert' type-v2='layout-flow' w='9668' x='89849' y='986'>
              <zone h='8877' id='8' name='Default Probability by EBITDA' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' type-v2='color' w='9668' x='89849' y='986'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone h='16646' id='9' name='Default Probability by EBITDA' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' type-v2='size' w='9668' x='89849' y='9863'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
              <zone fixed-size='548' forceUpdate='true' h='68557' id='17' is-fixed='true' type-v2='text' w='9668' x='89849' y='26509'>
                <formatted-text>
                  <run fontcolor='#555555' fontname='Arial Rounded MT Bold' fontsize='13'>Based on the four graphs, it appears that as EBITDA, Profit Margin, and Liquidity Ratio increase, the default probability decreases. Whereas when Debt to Equity increases so does the default probablity.</run>
                </formatted-text>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='none' />
                  <format attr='border-width' value='0' />
                  <format attr='margin' value='4' />
                  <format attr='background-color' value='#faf4e7' />
                </zone-style>
              </zone>
            </zone>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
          </zone-style>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <size maxheight='1400' minheight='1400' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='19' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98028' id='18' param='vert' type-v2='layout-flow' w='99034' x='483' y='986'>
                <zone fixed-size='280' h='49014' id='3' is-fixed='true' name='Default Probability by EBITDA' w='44683' x='483' y='986'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone h='8877' id='8' name='Default Probability by EBITDA' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' type-v2='color' w='9668' x='89849' y='986'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='16646' id='9' name='Default Probability by EBITDA' pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' type-v2='size' w='9668' x='89849' y='9863'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='49014' id='10' is-fixed='true' name='Default Probability by Debt to Equity Ratio' w='44683' x='45166' y='986'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='548' forceUpdate='true' h='68557' id='17' type-v2='text' w='9668' x='89849' y='26509'>
                  <formatted-text>
                    <run fontcolor='#555555' fontname='Arial Rounded MT Bold' fontsize='13'>Based on the four graphs, it appears that as EBITDA, Profit Margin, and Liquidity Ratio increase, the default probability decreases. Whereas when Debt to Equity increases so does the default probablity.</run>
                  </formatted-text>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='49014' id='11' is-fixed='true' name='Default Probability by Profit Margin' w='44683' x='483' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='49014' id='12' is-fixed='true' name='Default Probability by Liquidity Ratio' w='44683' x='45166' y='50000'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#faf4e7' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{8B5F9277-69E4-4297-8865-FD64D1A16F0A}' />
    </dashboard>
  </dashboards>
  <windows source-height='37'>
    <window class='worksheet' name='Default Rate by Industry'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Industry_Sector:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{7FD331A4-A9A4-4460-9E2D-64EE6E76AD2B}' />
    </window>
    <window class='worksheet' name='Geographic Distribution Map'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[avg:Default_Probability:qk]' type='color' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{E4812CEA-13C4-415A-9521-C45D7D63BDB1}' />
    </window>
    <window class='worksheet' name='Default Rate By Business'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{E3DD9C57-4E14-42F1-85A7-EB2F508979B0}' />
    </window>
    <window class='worksheet' name='Risk Distribution'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Risk_Category:nk]' type='color' />
            <card param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Risk_Category:nk]' type='highlighter' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{2C7D2E00-CA91-4188-9372-6E02943BFF92}' />
    </window>
    <window class='dashboard' name='Dashboard 1'>
      <viewpoints>
        <viewpoint name='Default Rate By Business'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='Default Rate by Industry' />
        <viewpoint name='Geographic Distribution Map' />
        <viewpoint name='Risk Distribution' />
      </viewpoints>
      <active id='12' />
      <simple-id uuid='{40FCCBAD-1B83-42C2-8E9B-5051D0639BC3}' />
    </window>
    <window class='worksheet' name='Default Probability by EBITDA'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' type='color' />
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' type='size' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{ABE2BF9F-2085-42A8-8328-5DA4F927010B}' />
    </window>
    <window class='worksheet' name='Default Probability by Debt to Equity Ratio'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' type='color' />
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' type='size' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{FB27E0D3-4A32-47E8-BC7B-E1F119455E00}' />
    </window>
    <window class='worksheet' name='Default Probability by Profit Margin'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' type='color' />
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' type='size' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{70DC8AD0-8B88-4B6F-8EC2-F533614DBB56}' />
    </window>
    <window class='worksheet' name='Default Probability by Liquidity Ratio'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[none:Loan_Default_Status:ok]' type='color' />
            <card pane-specification-id='0' param='[federated.060ig3j0nvmkqp16e1g8c1nn1twz].[sum:Loan_Amount_Requested:qk]' type='size' />
          </strip>
        </edge>
      </cards>
      <simple-id uuid='{C1855525-4410-4F1C-A43B-7AE0570B1BE6}' />
    </window>
    <window class='dashboard' maximized='true' name='Dashboard 2'>
      <viewpoints>
        <viewpoint name='Default Probability by Debt to Equity Ratio'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='Default Probability by EBITDA'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='Default Probability by Liquidity Ratio'>
          <zoom type='entire-view' />
        </viewpoint>
        <viewpoint name='Default Probability by Profit Margin'>
          <zoom type='entire-view' />
        </viewpoint>
      </viewpoints>
      <active id='8' />
      <simple-id uuid='{16B89FB2-5DCF-45E5-9609-ECBA6B383D25}' />
    </window>
  </windows>
</workbook>
