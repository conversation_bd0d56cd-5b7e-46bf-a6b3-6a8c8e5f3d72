{"cells": [{"cell_type": "markdown", "id": "f4117583", "metadata": {}, "source": ["# SME Loan Default Prediction - Model Building\n", "\n", "This notebook implements a machine learning pipeline to predict SME loan defaults using both traditional financial metrics and alternative data.\n", "\n", "## Workflow\n", "1. Data Preparation\n", "   - Load and merge datasets\n", "   - Feature engineering\n", "   - Handle class imbalance\n", "2. Model Development\n", "   - Split data into train/test sets\n", "   - Train baseline model\n", "   - Train advanced models\n", "   - Hyperparameter tuning\n", "3. Model Evaluation\n", "   - Performance metrics\n", "   - Feature importance analysis\n", "   - Model comparison"]}, {"cell_type": "code", "execution_count": 12, "id": "344673b4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "from sklearn.model_selection import train_test_split, GridSearchCV\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import confusion_matrix, classification_report, roc_auc_score, roc_curve\n", "from imblearn.over_sampling import SMOTE\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import joblib\n", "import os  # Add os module import\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# Plot settings\n", "plt.style.use('seaborn-v0_8-whitegrid')\n", "sns.set_theme()\n", "plt.rcParams['figure.figsize'] = (12, 8)"]}, {"cell_type": "code", "execution_count": 13, "id": "b7ae520e", "metadata": {}, "outputs": [], "source": ["# Load the datasets\n", "sme_profiles = pd.read_csv('data/sme_profiles.csv')\n", "sme_financials = pd.read_csv('data/sme_financials.csv')\n", "sme_alternative_data = pd.read_csv('data/sme_alternative_data.csv')\n", "sme_loan_applications = pd.read_csv('data/sme_loan_applications.csv')\n", "\n", "# Convert date columns\n", "sme_loan_applications['Loan_Application_Date'] = pd.to_datetime(sme_loan_applications['Loan_Application_Date'])\n", "sme_loan_applications['Observation_EndDate'] = pd.to_datetime(sme_loan_applications['Observation_EndDate'])\n", "sme_loan_applications['Application_Year'] = sme_loan_applications['Loan_Application_Date'].dt.year"]}, {"cell_type": "markdown", "id": "d857c777", "metadata": {}, "source": ["## Feature Engineering\n", "\n", "Let's create relevant features for our model by combining financial metrics and alternative data:"]}, {"cell_type": "code", "execution_count": 14, "id": "6a02d6d7", "metadata": {}, "outputs": [], "source": ["# Get financial data from the year before loan application\n", "sme_loan_applications['Previous_Year'] = sme_loan_applications['Application_Year'] - 1\n", "loan_financials = sme_loan_applications.merge(\n", "    sme_financials,\n", "    left_on=['SME_ID', 'Previous_Year'],\n", "    right_on=['SME_ID', 'Year']\n", ")\n", "\n", "# Calculate financial ratios\n", "def calculate_financial_ratios(df):\n", "    # Profitability ratios\n", "    df['Profit_Margin'] = df['Net_Income'] / df['Revenue']\n", "    df['Return_on_Assets'] = df['Net_Income'] / df['Total_Assets']\n", "    df['Operating_Margin'] = (df['Revenue'] - df['Operating_Expense']) / df['Revenue']\n", "    \n", "    # Leverage ratios\n", "    df['Debt_to_Equity'] = df['Total_Liabilities'] / df['Equity']\n", "    df['Liability_to_Asset'] = df['Total_Liabilities'] / df['Total_Assets']\n", "    \n", "    # Liquidity ratios\n", "    df['Current_Ratio'] = (df['Cash_Holdings'] + df['Accounts_Receivable']) / df['Accounts_Payable']\n", "    df['Cash_Ratio'] = df['Cash_Holdings'] / df['Accounts_Payable']\n", "    df['Working_Capital_Ratio'] = (df['Cash_Holdings'] + df['Accounts_Receivable'] - df['Accounts_Payable']) / df['Total_Assets']\n", "    \n", "    # Efficiency ratios\n", "    df['Asset_Turnover'] = df['Revenue'] / df['Total_Assets']\n", "    df['Inventory_Turnover'] = df['COGS'] / df['Inventory']\n", "    df['Receivables_Turnover'] = df['Revenue'] / df['Accounts_Receivable']\n", "    \n", "    return df\n", "\n", "# Calculate ratios\n", "loan_data = calculate_financial_ratios(loan_financials)\n", "\n", "# Merge with alternative data\n", "loan_data = loan_data.merge(sme_alternative_data, on='SME_ID')\n", "\n", "# Merge with SME profiles\n", "loan_data = loan_data.merge(sme_profiles[['SME_ID', 'Industry_Sector', 'Geographic_Location', \n", "                                        'Years_In_Business', 'Geographic_Location_Risk_Tier']], \n", "                          on='SME_ID')\n", "\n", "# Create dummy variables for categorical features\n", "loan_data = pd.get_dummies(loan_data, \n", "                          columns=['Industry_Sector', 'Geographic_Location'],\n", "                          drop_first=True)"]}, {"cell_type": "markdown", "id": "d8936ea9", "metadata": {}, "source": ["## Feature Selection and Data Preparation"]}, {"cell_type": "code", "execution_count": 15, "id": "bb0d3501", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class distribution before SMOTE:\n", "Loan_Default_Status\n", "0    0.616162\n", "1    0.383838\n", "Name: proportion, dtype: float64\n", "\n", "Class distribution after SMOTE:\n", "Loan_Default_Status\n", "0    0.5\n", "1    0.5\n", "Name: proportion, dtype: float64\n"]}], "source": ["# Select features for modeling\n", "financial_features = [\n", "    'Profit_Margin', 'Return_on_Assets', 'Operating_Margin',\n", "    'Debt_to_Equity', 'Liability_to_Asset', \n", "    'Current_Ratio', '<PERSON>_Ratio', 'Working_Capital_Ratio',\n", "    'Asset_Turnover', 'Inventory_Turnover', 'Receivables_Turnover'\n", "]\n", "\n", "alternative_features = [\n", "    'Online_Presence_Score', 'Average_Customer_Review_Score',\n", "    'Number_of_Customer_Complaints_Monthly', 'Payment_Gateway_Transaction_Volatility'\n", "]\n", "\n", "business_features = [\n", "    'Years_In_Business', 'Geographic_Location_Risk_Tier'\n", "]\n", "\n", "# Get dummy column names\n", "dummy_columns = [col for col in loan_data.columns \n", "                if col.startswith(('Industry_Sector_', 'Geographic_Location_'))]\n", "\n", "# Combine all features\n", "feature_columns = financial_features + alternative_features + business_features + dummy_columns\n", "\n", "# Prepare X and y\n", "X = loan_data[feature_columns]\n", "y = loan_data['Loan_Default_Status']\n", "\n", "# Split the data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "# Scale the features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Handle class imbalance using SMOTE\n", "smote = SMOTE(random_state=42)\n", "X_train_balanced, y_train_balanced = smote.fit_resample(X_train_scaled, y_train)\n", "\n", "# Print class distribution before and after SMOTE\n", "print(\"Class distribution before SMOTE:\")\n", "print(pd.Series(y_train).value_counts(normalize=True))\n", "print(\"\\nClass distribution after SMOTE:\")\n", "print(pd.Series(y_train_balanced).value_counts(normalize=True))"]}, {"cell_type": "markdown", "id": "affe3b2f", "metadata": {}, "source": ["## Model Development and Training"]}, {"cell_type": "code", "execution_count": 16, "id": "9602f32a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Training Logistic Regression...\n", "\n", "Logistic Regression Results:\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.74      0.55      0.63       123\n", "           1       0.49      0.68      0.57        76\n", "\n", "    accuracy                           0.60       199\n", "   macro avg       0.61      0.62      0.60       199\n", "weighted avg       0.64      0.60      0.61       199\n", "\n", "Best parameters: {'C': 0.1, 'class_weight': None, 'max_iter': 1000}\n", "Best cross-validation score: 0.651\n", "\n", "Training Random Forest...\n", "\n", "Logistic Regression Results:\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.74      0.55      0.63       123\n", "           1       0.49      0.68      0.57        76\n", "\n", "    accuracy                           0.60       199\n", "   macro avg       0.61      0.62      0.60       199\n", "weighted avg       0.64      0.60      0.61       199\n", "\n", "Best parameters: {'C': 0.1, 'class_weight': None, 'max_iter': 1000}\n", "Best cross-validation score: 0.651\n", "\n", "Training Random Forest...\n", "\n", "Random Forest Results:\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.63      0.66       123\n", "           1       0.47      0.53      0.50        76\n", "\n", "    accuracy                           0.59       199\n", "   macro avg       0.58      0.58      0.58       199\n", "weighted avg       0.60      0.59      0.60       199\n", "\n", "Best parameters: {'max_depth': None, 'min_samples_leaf': 2, 'min_samples_split': 2, 'n_estimators': 100}\n", "Best cross-validation score: 0.723\n", "\n", "Random Forest Results:\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.68      0.63      0.66       123\n", "           1       0.47      0.53      0.50        76\n", "\n", "    accuracy                           0.59       199\n", "   macro avg       0.58      0.58      0.58       199\n", "weighted avg       0.60      0.59      0.60       199\n", "\n", "Best parameters: {'max_depth': None, 'min_samples_leaf': 2, 'min_samples_split': 2, 'n_estimators': 100}\n", "Best cross-validation score: 0.723\n"]}], "source": ["def train_evaluate_model(model, X_train, y_train, X_test, y_test, model_name):\n", "    \"\"\"Train and evaluate a model with detailed metrics\"\"\"\n", "    \n", "    # Train the model\n", "    model.fit(X_train, y_train)\n", "    \n", "    # Make predictions\n", "    y_pred = model.predict(X_test)\n", "    y_pred_proba = model.predict_proba(X_test)[:, 1]\n", "    \n", "    # Calculate metrics\n", "    print(f\"\\n{model_name} Results:\")\n", "    print(\"\\nClassification Report:\")\n", "    print(classification_report(y_test, y_pred))\n", "    \n", "    # ROC Curve\n", "    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)\n", "    auc_score = roc_auc_score(y_test, y_pred_proba)\n", "    \n", "    return {\n", "        'model': model,\n", "        'predictions': y_pred,\n", "        'probabilities': y_pred_proba,\n", "        'roc_curve': (fpr, tpr, auc_score)\n", "    }\n", "\n", "# Initialize models with hyperparameter grids\n", "models = {\n", "    'Logistic Regression': {\n", "        'model': LogisticRegression(random_state=42),\n", "        'params': {\n", "            'C': [0.001, 0.01, 0.1, 1, 10],\n", "            'class_weight': ['balanced', None],\n", "            'max_iter': [1000]\n", "        }\n", "    },\n", "    'Random Forest': {\n", "        'model': RandomForestClassifier(random_state=42),\n", "        'params': {\n", "            'n_estimators': [100, 200],\n", "            'max_depth': [10, 20, None],\n", "            'min_samples_split': [2, 5],\n", "            'min_samples_leaf': [1, 2]\n", "        }\n", "    }\n", "}\n", "\n", "# Train and evaluate models with GridSearchCV\n", "results = {}\n", "for name, model_info in models.items():\n", "    print(f\"\\nTraining {name}...\")\n", "    \n", "    # Perform GridSearchCV\n", "    grid_search = GridSearchCV(\n", "        model_info['model'],\n", "        model_info['params'],\n", "        cv=5,\n", "        n_jobs=-1,\n", "        scoring='roc_auc'\n", "    )\n", "    \n", "    # Fit and evaluate\n", "    results[name] = train_evaluate_model(\n", "        grid_search,\n", "        X_train_balanced, y_train_balanced,\n", "        X_test_scaled, y_test,\n", "        name\n", "    )\n", "    \n", "    print(f\"Best parameters: {grid_search.best_params_}\")\n", "    print(f\"Best cross-validation score: {grid_search.best_score_:.3f}\")"]}, {"cell_type": "markdown", "id": "34817cdc", "metadata": {}, "source": ["## Model Evaluation and Visualization"]}, {"cell_type": "code", "execution_count": 17, "id": "a5d92ab9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Model Performance Summary:\n", "\n", "Logistic Regression:\n", "AUC-ROC Score: 0.686\n", "\n", "Random Forest:\n", "AUC-ROC Score: 0.623\n", "\n", "Best performing model: Logistic Regression\n"]}], "source": ["# Plot ROC curves\n", "plt.figure(figsize=(10, 6))\n", "for name, result in results.items():\n", "    fpr, tpr, auc_score = result['roc_curve']\n", "    plt.plot(fpr, tpr, label=f'{name} (AUC = {auc_score:.3f})')\n", "\n", "plt.plot([0, 1], [0, 1], 'k--')\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('ROC Curves Comparison')\n", "plt.legend()\n", "plt.show()\n", "\n", "# Feature importance analysis (using Random Forest)\n", "rf_model = results['Random Forest']['model'].best_estimator_\n", "feature_importance = pd.DataFrame({\n", "    'feature': feature_columns,\n", "    'importance': rf_model.feature_importances_\n", "})\n", "feature_importance = feature_importance.sort_values('importance', ascending=False)\n", "\n", "plt.figure(figsize=(12, 6))\n", "sns.barplot(data=feature_importance.head(15), x='importance', y='feature')\n", "plt.title('Top 15 Most Important Features')\n", "plt.xlabel('Feature Importance')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print summary of model performances\n", "print(\"\\nModel Performance Summary:\")\n", "for name, result in results.items():\n", "    _, _, auc_score = result['roc_curve']\n", "    print(f\"\\n{name}:\")\n", "    print(f\"AUC-ROC Score: {auc_score:.3f}\")\n", "    \n", "# Select best performing model\n", "best_model_name = max(results.items(), key=lambda x: x[1]['roc_curve'][2])[0]\n", "print(f\"\\nBest performing model: {best_model_name}\")"]}, {"cell_type": "markdown", "id": "3f8ffbfd", "metadata": {}, "source": ["## Model Persistence"]}, {"cell_type": "code", "execution_count": 18, "id": "b55f21d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Saved model artifacts:\n", "1. Best model: saved_models/best_model.joblib\n", "2. Feature scaler: saved_models/scaler.joblib\n", "3. Feature columns: saved_models/feature_columns.csv\n", "\n", "Model loading test:\n", "✓ Model and components successfully loaded and ready for predictions!\n"]}], "source": ["# Create directory for saved models if it doesn't exist\n", "if not os.path.exists('saved_models'):\n", "    os.makedirs('saved_models')\n", "\n", "# Save the best model and related components\n", "best_model = results[best_model_name]['model'].best_estimator_\n", "joblib.dump(best_model, 'saved_models/best_model.joblib')\n", "joblib.dump(scaler, 'saved_models/scaler.joblib')\n", "pd.Series(feature_columns).to_csv('saved_models/feature_columns.csv', index=False)\n", "\n", "print(\"\\nSaved model artifacts:\")\n", "print(\"1. Best model: saved_models/best_model.joblib\")\n", "print(\"2. Feature scaler: saved_models/scaler.joblib\")\n", "print(\"3. Feature columns: saved_models/feature_columns.csv\")\n", "\n", "# Example of how to load the model for predictions\n", "def load_model_for_prediction():\n", "    \"\"\"Load the saved model and related components\"\"\"\n", "    loaded_model = joblib.load('saved_models/best_model.joblib')\n", "    loaded_scaler = joblib.load('saved_models/scaler.joblib')\n", "    loaded_features = pd.read_csv('saved_models/feature_columns.csv')['0'].tolist()\n", "    return loaded_model, loaded_scaler, loaded_features\n", "\n", "print(\"\\nModel loading test:\")\n", "loaded_model, loaded_scaler, loaded_features = load_model_for_prediction()\n", "print(\"✓ Model and components successfully loaded and ready for predictions!\")"]}, {"cell_type": "markdown", "id": "3d8e2916", "metadata": {}, "source": ["## Making Predictions with New Data\n", "\n", "Here's how to use the saved model to make predictions for new SME loan applications:"]}, {"cell_type": "code", "execution_count": 19, "id": "95c1fa2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction Results:\n", "Default Probability: 83.65%\n", "Default Prediction: Yes\n", "Risk Tier: Very High Risk\n"]}], "source": ["def prepare_sme_features(sme_data):\n", "    \"\"\"Prepare features for a new SME loan application.\n", "    \n", "    Args:\n", "        sme_data (dict): Dictionary containing SME information with required fields\n", "        \n", "    Returns:\n", "        pd.DataFrame: Prepared feature vector ready for prediction\n", "    \"\"\"\n", "    # Create a single-row dataframe\n", "    df = pd.DataFrame([sme_data])\n", "    \n", "    # Calculate financial ratios\n", "    df['Profit_Margin'] = df['Net_Income'] / df['Revenue']\n", "    df['Return_on_Assets'] = df['Net_Income'] / df['Total_Assets']\n", "    df['Operating_Margin'] = (df['Revenue'] - df['Operating_Expense']) / df['Revenue']\n", "    df['Debt_to_Equity'] = df['Total_Liabilities'] / df['Equity']\n", "    df['Liability_to_Asset'] = df['Total_Liabilities'] / df['Total_Assets']\n", "    df['Current_Ratio'] = (df['Cash_Holdings'] + df['Accounts_Receivable']) / df['Accounts_Payable']\n", "    df['Cash_Ratio'] = df['Cash_Holdings'] / df['Accounts_Payable']\n", "    df['Working_Capital_Ratio'] = (df['Cash_Holdings'] + df['Accounts_Receivable'] - df['Accounts_Payable']) / df['Total_Assets']\n", "    df['Asset_Turnover'] = df['Revenue'] / df['Total_Assets']\n", "    df['Inventory_Turnover'] = df['COGS'] / df['Inventory']\n", "    df['Receivables_Turnover'] = df['Revenue'] / df['Accounts_Receivable']\n", "    \n", "    # Create dummy variables for categorical features\n", "    df = pd.get_dummies(df, columns=['Industry_Sector', 'Geographic_Location'], drop_first=True)\n", "    \n", "    # Ensure all required features are present\n", "    for feature in loaded_features:\n", "        if feature not in df.columns:\n", "            df[feature] = 0\n", "    \n", "    # Select and order features according to the model's requirements\n", "    return df[loaded_features]\n", "\n", "# Example usage with dummy data\n", "example_sme = {\n", "    'Revenue': 1000000,\n", "    'Net_Income': 150000,\n", "    'Operating_Expense': 700000,\n", "    'Total_Assets': 800000,\n", "    'Total_Liabilities': 400000,\n", "    'Equity': 400000,\n", "    'Cash_Holdings': 100000,\n", "    'Accounts_Receivable': 200000,\n", "    'Accounts_Payable': 150000,\n", "    'Inventory': 300000,\n", "    'COGS': 600000,\n", "    'Industry_Sector': 'Manufacturing',\n", "    'Geographic_Location': 'Urban',\n", "    'Years_In_Business': 5,\n", "    'Geographic_Location_Risk_Tier': 2,\n", "    'Online_Presence_Score': 7.5,\n", "    'Average_Customer_Review_Score': 4.2,\n", "    'Number_of_Customer_Complaints_Monthly': 3,\n", "    'Payment_Gateway_Transaction_Volatility': 0.15\n", "}\n", "\n", "# Prepare features\n", "X_new = prepare_sme_features(example_sme)\n", "\n", "# Scale features\n", "X_new_scaled = loaded_scaler.transform(X_new)\n", "\n", "# Make prediction\n", "default_prob = loaded_model.predict_proba(X_new_scaled)[0, 1]\n", "default_prediction = loaded_model.predict(X_new_scaled)[0]\n", "\n", "print(\"Prediction Results:\")\n", "print(f\"Default Probability: {default_prob:.2%}\")\n", "print(f\"Default Prediction: {'Yes' if default_prediction == 1 else 'No'}\")\n", "\n", "# You could also add risk tiers based on probability\n", "def get_risk_tier(probability):\n", "    if probability < 0.2:\n", "        return \"Low Risk\"\n", "    elif probability < 0.4:\n", "        return \"Moderate Risk\"\n", "    elif probability < 0.6:\n", "        return \"Medium Risk\"\n", "    elif probability < 0.8:\n", "        return \"High Risk\"\n", "    else:\n", "        return \"Very High Risk\"\n", "\n", "print(f\"Risk Tier: {get_risk_tier(default_prob)}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}